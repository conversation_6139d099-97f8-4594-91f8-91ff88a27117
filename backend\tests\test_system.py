"""
智能康复系统 - 系统测试脚本
测试各个组件的基本功能
"""
import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试数据模型
        from models.data_models import ZMQDetectData, SystemStateData
        from models.system_states import SystemState, StateTransitionEvent
        from models.constants import SystemConfig, KeyPointMapping
        print("✓ 数据模型导入成功")
        
        # 测试服务组件
        from services.streaming.mjpeg_stream import mjpeg_stream
        from services.communication.zmq.zmq_receiver import zmq_receiver
        from backend.services.communication.websocket.websocket_manager import websocket_handler
        from services.business.state_manager import state_manager
        from services.business.user.user_manager import user_manager
        print("✓ 服务组件导入成功")
        
        # 测试算法组件
        from algorithms.action_recognition import action_recognizer
        from algorithms.pose_analyzer import pose_analyzer
        from backend.algorithms.action_scorer import action_scorer
        print("✓ 算法组件导入成功")
        
        # 测试工具组件
        from utils.data_validator import schema_validator, data_converter
        print("✓ 工具组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_user_manager():
    """测试用户管理器"""
    print("\n测试用户管理器...")
    
    try:
        from services.business.user.user_manager import user_manager
        
        # 测试用户验证
        user_info = user_manager.authenticate_user("P001")
        if user_info:
            print(f"✓ 用户验证成功: {user_info.name}")
        else:
            print("✗ 用户验证失败")
            return False
        
        # 测试获取用户任务
        actions = user_manager.get_user_actions("P001")
        print(f"✓ 获取用户任务成功: {len(actions)} 个任务")
        
        return True
        
    except Exception as e:
        print(f"✗ 用户管理器测试失败: {e}")
        return False

def test_state_manager():
    """测试状态管理器"""
    print("\n测试状态管理器...")
    
    try:
        from services.business.state_manager import state_manager
        from models.system_states import StateTransitionEvent
        
        # 测试获取当前状态
        current_state = state_manager.get_current_state()
        print(f"✓ 当前状态: {current_state.value}")
        
        # 测试状态转换
        success = state_manager.transition_to(StateTransitionEvent.USER_DETECTED)
        if success:
            print("✓ 状态转换成功")
        else:
            print("✗ 状态转换失败")
        
        # 重置状态
        state_manager.reset_to_idle()
        print("✓ 状态重置成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 状态管理器测试失败: {e}")
        return False

def test_websocket_handler():
    """测试WebSocket处理器"""
    print("\n测试WebSocket处理器...")
    
    try:
        from backend.services.communication.websocket.websocket_manager import websocket_handler
        
        # 测试获取统计信息
        stats = websocket_handler.get_stats()
        print(f"✓ WebSocket统计信息: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ WebSocket处理器测试失败: {e}")
        return False

def test_mjpeg_stream():
    """测试MJPEG流生成器"""
    print("\n测试MJPEG流生成器...")
    
    try:
        from services.streaming.mjpeg_stream import mjpeg_stream
        
        # 测试添加帧（模拟数据）
        test_frame = "ffd8ffe000104a46494600010101006000600000"  # 简单的JPEG头
        mjpeg_stream.add_frame(test_frame)
        print("✓ 添加测试帧成功")
        
        # 测试活跃状态
        is_active = mjpeg_stream.is_active()
        print(f"✓ 流状态: {'活跃' if is_active else '非活跃'}")
        
        return True
        
    except Exception as e:
        print(f"✗ MJPEG流生成器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("智能康复系统 - 组件测试")
    print("=" * 50)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_imports,
        test_user_manager,
        test_state_manager,
        test_websocket_handler,
        test_mjpeg_stream
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统组件正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
