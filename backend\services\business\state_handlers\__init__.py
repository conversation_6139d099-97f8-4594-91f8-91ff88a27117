"""
智能康复系统 - 状态处理器工厂
使用简单工厂模式管理各个状态处理器
"""
import logging
import time
from typing import Dict, Optional, Any
from models.system_states import SystemState

class BaseStateHandler:
    """状态处理器基类"""
    
    def __init__(self, state: SystemState):
        """初始化状态处理器"""
        self.state = state
        self.logger = logging.getLogger(f"{__name__}.{state.value}")
    
    def enter_state(self, context: Dict[str, Any]) :
        """
        进入状态时的处理逻辑
        
        Args:
            context: 状态上下文
            
        Returns:
            Dict: 处理结果
        """
        context.update({"enter_state_time": time.time()})
        self.logger.info(f"进入状态: {context.get('enter_state_time', 0)}")
      

    def handle_data(self, data: Any, context: Dict[str, Any]):
        """
        处理数据
        
        Args:
            data: 输入数据
            context: 状态上下文
            
        Returns:
            Dict: 处理结果
        """
        return {"success": True, "message": "数据处理完成"}
    
    def exit_state(self, context: Dict[str, Any]):
        """
        退出状态时的处理逻辑

        Args:
            context: 状态上下文

        Returns:
            Dict: 处理结果
        """
        self.logger.info(f"退出状态: {self.state.value}")



class StateHandlerFactory:
    """状态处理器工厂"""
    
    def __init__(self):
        """初始化工厂"""
        self.logger = logging.getLogger(__name__)
        self._handlers: Dict[SystemState, BaseStateHandler] = {}
        self._initialize_handlers()
    
    def _initialize_handlers(self):
        """初始化所有状态处理器 - 重构版本"""
        try:
            # 延迟导入避免循环依赖
            from .waiting_handler import WaitingHandler
            from .introduction_handler import IntroductionHandler
            from .preparation_handler import PreparationHandler
            from .training_handler import TrainingHandler
            from .reporting_handler import ReportingHandler
            from .pause_handler import PauseHandler

            # 注册所有状态处理器
            self._handlers = {
                SystemState.WAITING: WaitingHandler(),
                SystemState.INTRODUCTION: IntroductionHandler(),
                SystemState.PREPARATION: PreparationHandler(),
                SystemState.TRAINING: TrainingHandler(),
                SystemState.REPORTING: ReportingHandler(),
                SystemState.PAUSE: PauseHandler()
            }

            self.logger.info(f"状态处理器工厂初始化完成: {len(self._handlers)} 个处理器")

        except Exception as e:
            self.logger.error(f"状态处理器工厂初始化失败: {e}")
            self._handlers = {}
    
    def get_handler(self, state: SystemState) -> Optional[BaseStateHandler]:
        """
        获取状态处理器
        
        Args:
            state: 系统状态
            
        Returns:
            BaseStateHandler: 状态处理器，不存在返回None
        """
        return self._handlers.get(state)
    
    def get_all_handlers(self) -> Dict[SystemState, BaseStateHandler]:
        """获取所有状态处理器"""
        return self._handlers.copy()
    
    def reload_handlers(self):
        """重新加载所有处理器"""
        self.logger.info("重新加载状态处理器")
        self._initialize_handlers()

# 全局状态处理器工厂实例
state_handler_factory = StateHandlerFactory()
