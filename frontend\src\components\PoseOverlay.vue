<!-- components/PoseOverlay.vue -->
<template>
  <canvas ref="poseCanvas" class="pose-overlay"></canvas>
</template>

<script setup>
import { ref, watch, onUnmounted, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useMainStore } from '@/stores/main';
import { KeypointRenderer } from '@/utils/keypointRenderer';
import { COCO_WHOLEBODY_CONNECTIONS } from '@/utils/poseConstants';

const props = defineProps({
  targetRef: {
    type: Object, // 接收持有VideoStream组件实例的ref
    default: null,
  },
  poseRenderOptions: {
    type: Object,
    default: () => ({
      pointRadius: 4,
      lineWidth: 2,
      pointColor: 'red',
      lineColor: 'lime',
      confidenceThreshold: 0.3,
      smoothingFactor: 0.4, // 新增：平滑因子
    }),
  },
});

const mainStore = useMainStore();
const { poseKeypoints } = storeToRefs(mainStore);

const poseCanvas = ref(null);
const poseRenderer = ref(null);
let resizeObserver = null;

const setupCanvas = (containerEl, imageEl) => {
  if (!poseCanvas.value || !containerEl || !imageEl) return;

  const rect = containerEl.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) return;

  const canvas = poseCanvas.value;
  const dpr = window.devicePixelRatio || 1;
  
  canvas.width = rect.width * dpr;
  canvas.height = rect.height * dpr;
  canvas.style.width = `${rect.width}px`;
  canvas.style.height = `${rect.height}px`;

  const ctx = canvas.getContext('2d');
  ctx.scale(dpr, dpr);

  if (!poseRenderer.value) {
    poseRenderer.value = new KeypointRenderer(canvas, {
      ...props.poseRenderOptions,
      connections: COCO_WHOLEBODY_CONNECTIONS,
    });
    poseRenderer.value.startRenderLoop();
  } else {
    poseRenderer.value.updateCanvas(canvas);
  }
  
  // 关键：每次更新时都传递 image 元素给渲染器
  poseRenderer.value.updateImageElement(imageEl);
};

watch(
  () => props.targetRef,
  async (componentInstance) => {
    const containerEl = componentInstance?.containerRef;
    const imageEl = componentInstance?.imageRef;

    if (containerEl && imageEl) {
      await nextTick();
      if (poseCanvas.value) {
        setupCanvas(containerEl, imageEl);

        if (resizeObserver) resizeObserver.disconnect();
        
        resizeObserver = new ResizeObserver(() => {
          setupCanvas(containerEl, imageEl);
        });
        resizeObserver.observe(containerEl);
      }
    }
  },
  { deep: true, flush: 'post' }
);

watch(poseKeypoints, (newKeypoints) => {
  if (poseRenderer.value) {
    poseRenderer.value.updateKeypoints(newKeypoints || []);
  }
}, { deep: true });

onUnmounted(() => {
  if (resizeObserver) resizeObserver.disconnect();
  if (poseRenderer.value) poseRenderer.value.stopRenderLoop();
});
</script>

<style scoped>
.pose-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: transparent;
  z-index: 10;
}
</style>