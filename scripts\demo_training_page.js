#!/usr/bin/env node

/**
 * 训练页面演示脚本
 * 用于展示训练页面的各种功能和状态
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🏥 智能康复系统 - 训练页面演示');
console.log('=====================================');

// 模拟训练数据
const mockTrainingData = {
  user: {
    name: '张三',
    patient_id: 'P001',
    age: 45,
    condition: '肩关节康复'
  },
  
  actions: [
    {
      action_type: 'shoulder_touch',
      side: 'left',
      description: '左手摸右肩膀',
      target_score: 80,
      duration: 30
    },
    {
      action_type: 'shoulder_touch', 
      side: 'right',
      description: '右手摸左肩膀',
      target_score: 80,
      duration: 30
    },
    {
      action_type: 'arm_raise',
      side: 'left',
      description: '左臂上抬',
      target_score: 75,
      duration: 25
    },
    {
      action_type: 'arm_raise',
      side: 'right', 
      description: '右臂上抬',
      target_score: 75,
      duration: 25
    }
  ],

  states: ['preparation', 'training', 'pause', 'reporting']
};

// 当前状态
let currentState = 'preparation';
let currentActionIndex = 0;
let currentScore = 0;
let trainingTime = 0;

function displayHeader() {
  console.clear();
  console.log('🏥 智能康复系统 - 训练页面演示');
  console.log('=====================================');
  console.log(`👤 用户: ${mockTrainingData.user.name} (${mockTrainingData.user.patient_id})`);
  console.log(`📊 状态: ${getStateText(currentState)}`);
  console.log(`🎯 当前动作: ${getCurrentActionText()}`);
  console.log(`⭐ 得分: ${currentScore}/100`);
  console.log(`⏱️  训练时长: ${formatTime(trainingTime)}`);
  console.log('=====================================');
}

function getStateText(state) {
  const stateMap = {
    'preparation': '🔄 准备中',
    'training': '🏃 训练中', 
    'pause': '⏸️  已暂停',
    'reporting': '📋 生成报告'
  };
  return stateMap[state] || state;
}

function getCurrentActionText() {
  const action = mockTrainingData.actions[currentActionIndex];
  if (!action) return '无';
  
  const sideText = action.side === 'left' ? '左侧' : '右侧';
  const typeMap = {
    'shoulder_touch': '摸肩膀',
    'arm_raise': '手臂上抬',
    'finger_touch': '对指训练',
    'palm_flip': '手掌翻转'
  };
  
  return `${typeMap[action.action_type] || action.action_type} (${sideText})`;
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

function displayActionList() {
  console.log('\n📋 训练任务列表:');
  console.log('─────────────────');
  
  mockTrainingData.actions.forEach((action, index) => {
    const status = index < currentActionIndex ? '✅' : 
                  index === currentActionIndex ? '🔄' : '⏳';
    const sideText = action.side === 'left' ? '左侧' : '右侧';
    const typeMap = {
      'shoulder_touch': '摸肩膀',
      'arm_raise': '手臂上抬', 
      'finger_touch': '对指训练',
      'palm_flip': '手掌翻转'
    };
    
    console.log(`${status} ${index + 1}. ${typeMap[action.action_type]} (${sideText})`);
    
    if (index === currentActionIndex && currentState === 'training') {
      const progress = Math.round((currentScore / action.target_score) * 100);
      const progressBar = '█'.repeat(Math.floor(progress / 5)) + 
                         '░'.repeat(20 - Math.floor(progress / 5));
      console.log(`   进度: [${progressBar}] ${progress}%`);
    }
  });
}

function displayVideoArea() {
  console.log('\n📹 实时画面区域:');
  console.log('─────────────────');
  console.log('┌─────────────────────────────┐');
  console.log('│                             │');
  console.log('│      📷 摄像头画面           │');
  console.log('│                             │');
  console.log('│   🔴 姿态关键点检测中...      │');
  console.log('│                             │');
  console.log('│   帧率: 30 FPS              │');
  console.log('│   连接状态: ✅ 已连接        │');
  console.log('│                             │');
  console.log('└─────────────────────────────┘');
}

function displayStandardAction() {
  console.log('\n🎬 标准动作演示:');
  console.log('─────────────────');
  const action = mockTrainingData.actions[currentActionIndex];
  if (action) {
    console.log(`📺 正在播放: ${getCurrentActionText()}`);
    console.log('┌─────────────────┐');
    console.log('│   🎥 演示视频    │');
    console.log('│                │');
    console.log('│   ▶️ 播放中...   │');
    console.log('│                │');
    console.log('└─────────────────┘');
    console.log('\n💡 动作要点:');
    console.log('• 保持身体稳定，动作缓慢');
    console.log('• 注意动作幅度和准确性');
    console.log('• 感到疼痛时立即停止');
  }
}

function displayFeedback() {
  console.log('\n📊 实时反馈信息:');
  console.log('─────────────────');
  
  if (currentState === 'preparation') {
    console.log('🔄 准备阶段');
    console.log('请观看标准动作演示，准备开始训练');
  } else if (currentState === 'training') {
    console.log(`🎯 动作状态: 进行中`);
    console.log(`⭐ 实时评分: ${currentScore}/100`);
    console.log(`⏱️  训练时长: ${formatTime(trainingTime)}`);
    
    // 模拟反馈消息
    const feedbackMessages = [
      '动作标准，继续保持！',
      '注意手臂高度',
      '很好，动作流畅',
      '稍微放慢速度'
    ];
    
    console.log('\n💬 最近消息:');
    feedbackMessages.slice(0, 3).forEach((msg, index) => {
      const time = new Date(Date.now() - index * 5000).toLocaleTimeString();
      console.log(`  ${time} - ${msg}`);
    });
  } else if (currentState === 'pause') {
    console.log('⏸️  训练已暂停');
    console.log('请返回摄像头前继续训练');
  }
}

function displayControls() {
  console.log('\n🎮 控制选项:');
  console.log('─────────────────');
  
  if (currentState === 'preparation') {
    console.log('1. 开始训练');
  } else if (currentState === 'training') {
    console.log('1. 暂停训练');
    console.log('2. 模拟动作评分');
  } else if (currentState === 'pause') {
    console.log('1. 恢复训练');
  }
  
  console.log('3. 切换到下一个动作');
  console.log('4. 模拟状态变化');
  console.log('5. 重置演示');
  console.log('0. 退出演示');
}

function handleUserInput(input) {
  const choice = input.trim();
  
  switch (choice) {
    case '1':
      if (currentState === 'preparation') {
        currentState = 'training';
        console.log('\n✅ 开始训练！');
      } else if (currentState === 'training') {
        currentState = 'pause';
        console.log('\n⏸️  训练已暂停');
      } else if (currentState === 'pause') {
        currentState = 'training';
        console.log('\n▶️  训练已恢复');
      }
      break;
      
    case '2':
      if (currentState === 'training') {
        currentScore = Math.min(100, currentScore + Math.floor(Math.random() * 20) + 5);
        trainingTime += Math.floor(Math.random() * 5) + 1;
        console.log(`\n📈 评分更新: ${currentScore}/100`);
      }
      break;
      
    case '3':
      currentActionIndex = (currentActionIndex + 1) % mockTrainingData.actions.length;
      currentScore = 0;
      currentState = 'preparation';
      console.log(`\n🔄 切换到动作: ${getCurrentActionText()}`);
      break;
      
    case '4':
      const states = ['preparation', 'training', 'pause', 'reporting'];
      const currentIndex = states.indexOf(currentState);
      currentState = states[(currentIndex + 1) % states.length];
      console.log(`\n🔄 状态变更为: ${getStateText(currentState)}`);
      break;
      
    case '5':
      currentState = 'preparation';
      currentActionIndex = 0;
      currentScore = 0;
      trainingTime = 0;
      console.log('\n🔄 演示已重置');
      break;
      
    case '0':
      console.log('\n👋 感谢使用智能康复系统演示！');
      rl.close();
      return;
      
    default:
      console.log('\n❌ 无效选项，请重新选择');
  }
  
  setTimeout(showDemo, 1000);
}

function showDemo() {
  displayHeader();
  displayActionList();
  displayVideoArea();
  displayStandardAction();
  displayFeedback();
  displayControls();
  
  rl.question('\n请选择操作 (输入数字): ', handleUserInput);
}

// 启动演示
console.log('\n🚀 正在启动训练页面演示...\n');
setTimeout(showDemo, 1000);
