"""
智能康复系统 - 数据模型定义
定义系统中使用的所有数据结构和类型
"""
from typing import List, Dict, Optional, Union, Literal, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from .system_states import SystemState
from .system_states import MessageType
# 动作类型枚举
ActionType = Literal[
    "shoulder_touch",    # 摸肩膀
    "arm_raise",         # 手臂上抬
    "finger_touch",      # 对指
    "palm_flip"          # 手掌翻转
]

# 训练侧别枚举
TrainingSide = Literal["left", "right"]

@dataclass
class ZMQDetectData:
    """ZMQ检测数据结构"""
    timestamp: float
    patient_id: str
    pose_keypoints: List[List[float]]  # [[x,y,confidence], ...] 三元组格式，133个关键点
    pose_bbox: List[float]  # [x1, y1, x2, y2]
    face_bbox: List[float]  # [x1, y1, x2, y2]

@dataclass
class ZMQCameraFrame:
    """ZMQ摄像头帧数据结构"""
    timestamp: float
    frame_data: str  # hex编码的JPEG数据
    frame_shape: List[int]  # [height, width]

@dataclass
class UserInfo:
    """用户信息"""
    patient_id: str
    name: str
    age: Optional[int] = None
    gender: Optional[str] = None
    last_login: Optional[datetime] = None

@dataclass
class ActionInfo:
    """基本任务信息"""
    action_type: ActionType
    side: TrainingSide
    difficulty_level: Literal["easy", "medium", "hard"]
    required_keypoints: List[int]  # 需要的关键点索引
    video_url: Optional[str] = None  # 动作演示视频URL

@dataclass
class CurrentAction:
    """当前任务状态 - 扩展版本"""
    action_info: ActionInfo   # 任务基本信息
    action_type: ActionType  # 动作类型,
    side: TrainingSide  # 训练侧别
    difficulty_level: str = "medium"  # 动作难度
    completed: bool = False     # 当前组是否完成
    score: Optional[float] = None  # 当前动作实时得分
    action_status: str = "pending"    # 动作状态: pending, active, completed, failed
    start_time: Optional[float] = None    # 动作开始时间
    end_time: Optional[float] = None      # 动作结束时间
    completion_progress: float = 0.0  # 动作完成进度
    last_valid_time: Optional[float] = None  # 上次有效动作时间
    feedback_messages: List[str] = field(default_factory=list)  # 反馈消息列表
@dataclass
class TrainingSession:
    """训练会话数据 - 统一版本"""
    session_id: str
    patient_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    actions: List[ActionInfo] = field(default_factory=list)
    completed_actions: List[Dict] = field(default_factory=list)
    # 扩展字段
    session_status: str = "active"  # 会话状态: active, paused, completed, failed
    current_action_index: int = 0     # 当前任务索引
    total_actions_completed: int = 0 # 总完成动作数
    action_scores: Dict[str, float] = field(default_factory=dict)  # 各动作得分
    average_score: Optional[float] = None  # 总平均得分
    completion_rate: float = 0.0    # 完成率
    duration_seconds: float = 0.0   # 持续时间（秒）

    def get_duration_seconds(self) -> float:
        """获取会话持续时间（秒）"""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (datetime.now() - self.start_time).total_seconds()
        return 0.0

    def get_current_action(self) -> Optional[ActionInfo]:
        """获取当前任务"""
        if 0 <= self.current_action_index < len(self.actions):
            return self.actions[self.current_action_index]
        return None

    def is_completed(self) -> bool:
        """检查会话是否完成"""
        return self.session_status == "completed" or self.current_action_index >= len(self.actions)

    def calculate_completion_rate(self) -> float:
        """计算完成率"""
        if not self.actions:
            return 0.0
        return (self.current_action_index / len(self.actions)) * 100

    def add_action_score(self, action_key: str, score: float) -> None:
        """添加动作得分"""
        self.action_scores[action_key] = score
        self.total_actions_completed += 1
        # 重新计算平均分
        if self.action_scores:
            self.average_score = sum(self.action_scores.values()) / len(self.action_scores)
        # 更新完成率
        self.completion_rate = self.calculate_completion_rate()

@dataclass
class SystemStateData:
    """系统状态数据 - 增强版本"""
    current_state: SystemState
    current_action: Optional[CurrentAction] = None
    user_info: Optional[UserInfo] = None
    message: Optional[str] = None
    # 新增字段
    action_list: List[ActionInfo] = field(default_factory=list)  # 用户任务列表
    report_data: Optional[Dict[str, Any]] = None  # 训练报告数据
    face_box: Optional[List[float]] = None  # 人脸框
    patient_id: Optional[str] = None  # 检测到的用户ID
    training_session: Optional[TrainingSession] = None  # 训练会话数据
    pose_keypoints: List[List[float]] = field(default_factory=list)  # 关键点列表
    frame_data: Optional[str] = None  # 帧数据

@dataclass
class WebSocketMessage:
    """WebSocket消息格式 - 增强版本"""
    Union[
        SystemStateData,
        CurrentAction,
        TrainingSession,
        Dict[str, Any]
    ]
    message_id: Optional[str] = None  # 消息唯一标识


@dataclass
class ActionGuideInfo:
    """动作指导信息"""
    action_type: ActionType
    title: str
    description: str
    steps: List[str]
    key_points: List[str]
    common_errors: List[str]
    demonstration: Dict[str, Any]

class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_keypoint(data) -> bool:
        """验证关键点数据 - 三元组格式 [x, y, confidence]"""
        if isinstance(data, (list, tuple)) and len(data) == 3:
            return all(isinstance(v, (int, float)) for v in data)
        return False

    @staticmethod
    def validate_zmq_detect_data(data: Dict) -> bool:
        """验证ZMQ检测数据 - 支持新旧格式"""
        required_fields = ['timestamp', 'patient_id', 'pose_keypoints', 'pose_bbox', 'face_bbox']
        if not all(field in data for field in required_fields):
            return False

        # 验证关键点数量（RTMPose 133个关键点）
        pose_keypoints = data['pose_keypoints']
        if len(pose_keypoints) != 133:
            return False

        # 验证关键点格式
        for kp in pose_keypoints:
            if not DataValidator.validate_keypoint(kp):
                return False

        return True
    
    @staticmethod
    def validate_websocket_message(data: Dict) -> bool:
        """验证WebSocket消息"""
        required_fields = ['message_type', 'timestamp', 'data']
        return all(field in data for field in required_fields)

class DataSerializer:
    """数据序列化工具类"""
    @staticmethod
    def websocket_message_to_dict(message: Any) -> Dict:
        """WebSocket消息转字典"""
        return DataSerializer._serialize_data(message)
        

    @staticmethod
    def _serialize_data(data) -> Any:
        """递归序列化数据，处理枚举和复杂对象"""
        if data is None:
            return None
        elif isinstance(data, datetime):
            # 处理datetime对象，转换为ISO格式字符串
            return data.isoformat()
        elif isinstance(data, Enum):
            return data.value
        elif hasattr(data, '__dict__'):
            # 处理dataclass或其他对象
            result = {}
            for key, value in data.__dict__.items():
                result[key] = DataSerializer._serialize_data(value)
            return result
        elif isinstance(data, (list, tuple)):
            return [DataSerializer._serialize_data(item) for item in data]
        elif isinstance(data, dict):
            return {key: DataSerializer._serialize_data(value) for key, value in data.items()}
        else:
            # 基本类型直接返回
            return data
    
    @staticmethod
    def training_session_to_dict(session: TrainingSession) -> Dict:
        """训练会话转字典"""
        return {
            'session_id': session.session_id,
            'patient_id': session.patient_id,
            'start_time': session.start_time.isoformat(),
            'end_time': session.end_time.isoformat() if session.end_time else None,
            'actions': [action.__dict__ for action in session.actions],
            'completed_actions': session.completed_actions,
            'average_score': session.average_score
        }


class PoseDataConverter:
    """姿态数据格式转换工具类"""

    @staticmethod
    def validate_keypoints_format(keypoints: List[List[float]]) -> bool:
        """验证关键点三元组格式"""
        if not isinstance(keypoints, list) or len(keypoints) != 133:
            return False

        for kp in keypoints:
            if not isinstance(kp, list) or len(kp) != 3:
                return False
            if not all(isinstance(v, (int, float)) for v in kp):
                return False

        return True

    @staticmethod
    def safe_parse_keypoints(data) -> Optional[List[List[float]]]:
        """安全解析关键点数据 - 三元组格式"""
        try:
            if isinstance(data, list) and len(data) == 133:
                # 检查是否为三元组格式：[[x,y,confidence], ...]
                if all(isinstance(kp, list) and len(kp) == 3 for kp in data):
                    return data
            return None
        except Exception:
            return None

    @staticmethod
    def convert_zmq_detect_data(data_dict: Dict) -> Optional[ZMQDetectData]:
        """安全转换ZMQ检测数据"""
        try:
            # 解析关键点数据
            pose_keypoints = PoseDataConverter.safe_parse_keypoints(data_dict.get('pose_keypoints'))
            if pose_keypoints is None:
                return None

            return ZMQDetectData(
                timestamp=data_dict['timestamp'],
                patient_id=data_dict['patient_id'],
                pose_keypoints=pose_keypoints,
                pose_bbox=data_dict['pose_bbox'],
                face_bbox=data_dict['face_bbox']
            )
        except Exception:
            return None
        