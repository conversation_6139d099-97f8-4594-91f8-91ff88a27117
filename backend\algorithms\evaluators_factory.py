# src/algorithms/evaluator_factory.py

from typing import Type
from models.data_models import ActionInfo
from .evaluators.base_evaluator import BaseActionEvaluator
from .evaluators.arm_raise import ArmRaiseEvaluator
from .evaluators.shoulder_touch import ShoulderTouchEvaluator
from .evaluators.finger_touch import FingerTouchEvaluator
from .evaluators.palm_flip import PalmFlipEvaluator

class EvaluatorFactory:
    """
    评估器工厂，用于创建特定动作的评估器实例。
    """
    _evaluators = {
        "arm_raise": ArmRaiseEvaluator,
        "shoulder_touch": ShoulderTouchEvaluator,
        "finger_touch": FingerTouchEvaluator,
        "palm_flip": PalmFlipEvaluator,
    }

    @staticmethod
    def create_evaluator(action_info: ActionInfo) -> BaseActionEvaluator:
        """
        根据动作信息创建评估器实例。
        """
        evaluator_class = EvaluatorFactory._evaluators.get(action_info.action_type)
        if not evaluator_class:
            raise ValueError(f"未找到动作类型 '{action_info.action_type}' 的评估器")
        
        return evaluator_class(action_info)

evaluator_factory = EvaluatorFactory()