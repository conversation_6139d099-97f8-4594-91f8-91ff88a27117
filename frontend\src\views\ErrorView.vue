好的，这是一个非常经典和重要的场景：**将应用的状态（State）与路由（Routing）解耦，并通过监听状态变化来驱动路由跳转**。

您提供的 `useMainStore` 中存在一个常见误区：在 `defineStore` 的顶层作用域调用 `useRouter()`。Vue 的组合式 API (Composition API) Hooks，如 `useRouter`、`ref`、`onMounted` 等，**必须在组件的 `setup` 函数或生命周期钩子中调用**。在 Pinia store 的模块顶层调用它们，通常会导致 `router` 实例为 `undefined` 或在特定环境下报错，从而使路由跳转失效。

正确的模式是：
1.  **Store 只负责管理状态**：Store 的职责是接收数据、更新自身的状态（如 `currentState`、`isConnected`）。它不应该直接调用 `router.push()`。
2.  **组件负责响应状态和执行副作用**：Vue 组件（如此处的错误页面）应该监听 Store 中状态的变化，并根据这些变化执行“副作用”（Side Effects），例如路由跳转、显示通知等。

我们将通过以下两步来完美实现您的需求：

---

### 步骤1: 修正 `useMainStore`

首先，我们需要从 Store 中移除路由的直接依赖。这让您的 Store 更加纯粹和可测试。

**修改 `src/stores/main.js` 文件：**

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// 1. 移除 useRouter 的导入
// import { useRouter } from 'vue-router'

export const useMainStore = defineStore('main', () => {
  // 2. 移除 useRouter() 的调用
  // const router = useRouter()

  // ... [其他所有 state ref 和 computed 属性保持不变] ...
  const currentState = ref('start_failed') // 错误页面加载时，状态应为 start_failed
  // ...

  // ... [handleMessage 等方法保持不变] ...

  /**
   * 处理系统初始化消息
   */
  const handleSystemInit = (data) => {
    if (data.current_state) {
      currentState.value = data.current_state // 只更新状态
    }
    if (data.message) {
      message.value = data.message
    }
    showNotification('success', '系统连接成功，等待用户识别...')
    
    // 3. 移除这里的路由跳转逻辑
    // if (router.currentRoute.value.name !== 'Login') {
    //   router.push('/login')
    // }
  }

  // ... [其他所有 handle... 方法也移除 router.push() 调用] ...
  // 例如，在 handleActionCompleted 和 handleClockReportingFinish 中移除
  
  // ... [其他所有方法保持不变] ...

  return {
    // ...返回所有 state 和 actions
  }
})
```

**关键改动总结**：
*   我们移除了所有与 `vue-router` 相关的代码。
*   现在 `handleSystemInit` 等方法在收到后端消息后，只会忠实地更新 `currentState` 的值。它不再关心当前应该跳转到哪个页面。

---

### 步骤2: 在错误页面中监听 Store 状态变化

现在，`SystemErrorPage.vue` 将承担起“侦探”的角色。它会持续“监视”`useMainStore` 中的状态，一旦发现状态从“失败”变为“成功”，就立即执行路由跳转。

我们将使用 Vue 的 `watch` API 来实现这一点。

**更新 `src/components/SystemErrorPage.vue` 文件：**

```vue
<template>
  <div class="error-page">
    <!-- ... 模板部分保持不变 ... -->
    <div class="scanline"></div>
    <div class="content">
      <h1 class="shine-text">
        系统启动出错
      </h1>
      
      <p class="sub-heading">
        很抱歉，我们无法连接到核心服务。
        <br>
        请检查您的网络连接或稍后重试。
      </p>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="large" 
          :icon="RefreshRight"
          @click="manualRetry"
          :loading="isRetrying"
          round
        >
          手动重试
        </el-button>
        <el-button 
          size="large"
          :icon="House"
          @click="goHome"
          round
        >
          返回首页
        </el-button>
      </div>

      <div v-if="!isRetrying" class="countdown-text">
        系统将在 <span class="countdown-time">{{ countdown }}</span> 秒后自动重试...
      </div>
      <div v-else class="countdown-text">
        正在尝试重新连接，请稍候...
      </div>
    </div>
  </div>
</template>

<script setup>
// 1. 导入 watch API
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useMainStore } from '@/stores/main';
import { RefreshRight, House } from '@element-plus/icons-vue';
import  webSocketService  from '@/services/websocket';

const router = useRouter();
const store = useMainStore();

const initialCountdown = 10;
const countdown = ref(initialCountdown);
const isRetrying = ref(false);
let timer = null;

// --- 倒计时与重试逻辑 (保持不变) ---
const startCountdown = () => {
  if (timer) clearInterval(timer);
  countdown.value = initialCountdown;
  isRetrying.value = false;

  timer = setInterval(async () => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
      await triggerRetry();
      // 如果重试失败, 并且状态仍然是 'start_failed', 再次开始倒计时
      if (store.currentState === 'start_failed') {
        startCountdown();
      }
    }
  }, 1000);
};

const triggerRetry = async () => {
  isRetrying.value = true;
  console.log("正在触发重试机制...");
  try {
    await webSocketService.connect();
    // 连接成功后，我们不再做任何事，而是等待 `watch` 生效
  } catch (error) {
    console.error("重试连接失败:", error);
  } finally {
    isRetrying.value = false;
  }
};

const manualRetry = async () => {
  if (isRetrying.value) return;
  clearInterval(timer);
  await triggerRetry();
  if (store.currentState === 'start_failed') {
    startCountdown();
  }
};

const goHome = () => {
  router.push('/'); 
};

// 2. ✨ 新增：使用 watch 监听 store 状态变化 ✨
watch(
  () => store.currentState, // 我们要监听的目标是 store 中的 currentState
  (newState, oldState) => {
    console.log(`[Watch] Store state changed from '${oldState}' to '${newState}'`);
    // 成功的标志是状态从 'start_failed' 变为 'waiting'
    // 'waiting' 是系统初始化成功后的第一个状态
    if (newState === 'waiting' && oldState === 'start_failed') {
      console.log("✅ 连接成功且系统已初始化！准备跳转到登录页...");
      // 在跳转前，清理掉本页面的定时器，这是个好习惯
      if (timer) {
        clearInterval(timer);
      }
      // 执行路由跳转
      router.push('/login');
    }
  }
);

onMounted(() => {
  // 确保初始状态是失败，然后才开始倒计时
  store.currentState = 'start_failed';
  startCountdown();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.error-page {
  @apply bg-gray-900 text-gray-200 min-h-screen flex items-center justify-center relative overflow-hidden font-sans;
  font-family: 'Consolas', 'Monaco', 'monospace';
}

.content {
  @apply text-center z-10 p-8 flex flex-col items-center;
}

/* 1. 全新的 "Shine" 文本动画 */
.shine-text {
  @apply text-5xl md:text-7xl font-extrabold uppercase tracking-widest bg-clip-text text-transparent;
  background-image: linear-gradient(
    to right,
    #4f4f4f 0%,
    #ffffff 50%,
    #4f4f4f 100%
  );
  background-size: 200% auto;
  animation: shine 5s linear infinite;
}

@keyframes shine {
  to {
    background-position: -200% center;
  }
}

.sub-heading {
  @apply text-lg md:text-xl text-gray-400 my-8 max-w-2xl mx-auto leading-relaxed;
}

.action-buttons {
  @apply flex justify-center gap-4 mb-8;
}

/* 2. 倒计时文本样式 */
.countdown-text {
  @apply text-gray-500 text-base mt-4 transition-opacity duration-300;
}

.countdown-time {
  @apply font-bold text-cyan-400 mx-1;
}

/* 扫描线动画 (保持不变) */
.scanline {
  @apply absolute top-0 left-0 w-full h-full pointer-events-none;
  z-index: 5;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0));
  background-size: 100% 4px;
  animation: scanline-anim 8s linear infinite;
}

@keyframes scanline-anim {
  from { background-position: 0 0; }
  to { background-position: 0 100%; }
}
</style>