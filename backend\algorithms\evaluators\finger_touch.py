# src/algorithms/evaluators/finger_touch.py
from .base_evaluator import BaseActionEvaluator
# ... (imports) ...
from utils.pose_math import pose_math
class FingerTouchEvaluator(BaseActionEvaluator):
    def recognize(self) :
        # 简化识别：只要手在画面中
        hand = pose_math.get_keypoints(self.kps, [K.LEFT_WRIST if self.action_info.side == "left" else K.RIGHT_WRIST])
        if hand[0] is not None:
            return True, ""
        return False, "请将手部置于摄像头前"

    def score(self):
        # 由于静态评估困难，给予一个较高的基础分
        return 90.0, {}

    def generate_feedback(self, score: float, details: dict) -> list:
        return ["请按照节律完成指尖对触。"]