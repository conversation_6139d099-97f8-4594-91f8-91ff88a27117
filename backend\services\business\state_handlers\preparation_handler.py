"""
智能康复系统 - PREPARATION状态处理器
处理动作准备阶段，播放引导视频
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType, TimeConstants
from models.data_models import ZMQDetectData, SystemStateData, CurrentAction
from . import BaseStateHandler
from ..user_detection_service import user_detection_service

class PreparationHandler(BaseStateHandler):
    """PREPARATION状态处理器"""
    
    def __init__(self):
        """初始化PREPARATION状态处理器"""
        super().__init__(SystemState.PREPARATION)
        self.user_lost_start_time = None
        self.last_user_id = None
        self.current_action_index = 0
    
    def enter_state(self, context: Dict[str, Any]):
        """进入PREPARATION状态"""
        self.logger.info("系统进入动作准备状态")
    
    def handle_data(self, data: Any, context: Dict[str, Any]):
        """处理PREPARATION状态下的数据"""
        try:
            # 检查用户状态
            user_status = self._check_user_status(data, context)

            # 检查是否需要暂停
            if user_status["should_pause"]:
                return self._handle_pause(user_status, context)
            
        except Exception as e:
            self.logger.error(f"处理准备状态数据失败: {e}")
            # 错误情况下也要发送状态数据
         
    
    def handle_preparing_finish(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理前端发送的preparing_finish事件"""
        self.logger.info("前端引导视频播放完成，开始训练")
        
        current_action = context.get("current_action")
        if current_action:
            current_action.action_status = "active"
            current_action.start_time = time.time()
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.TRAINING,
            message="准备完成，开始训练！",
            current_action=current_action,
        )
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.PREPARING_FINISH,
            "next_state": SystemState.TRAINING,
            "state_data": state_data
        }
    
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        # 首先检查用户是否在画面中
        presence_result = user_detection_service.check_user(pose_data, context)

        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }
        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        pause_reason = user_status["pause_reason"]

        if pause_reason == "user_lost":
            event = StateTransitionEvent.USER_LOST
            message_type = MessageType.USER_LOST
        else:  # user_not_auth
            event = StateTransitionEvent.USER_NOT_AUTH
            message_type = MessageType.USER_NOT_AUTH

        # 创建暂停状态数据
        state_data = SystemStateData(
            current_state=SystemState.PAUSE,
            message=user_status["message"],
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )

        return {
            "success": True,
            "trigger_event": event,
            "next_state": SystemState.PAUSE,
            "websocket_message": message_type,
            "state_data": state_data,
            "message": user_status["message"]
        }
    

    def exit_state(self, context: Dict[str, Any]):
        """退出PREPARATION状态"""
        self.logger.info("系统退出动作准备状态")
        self.user_lost_start_time = None
