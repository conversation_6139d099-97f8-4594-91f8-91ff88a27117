"""
智能康复系统 - 系统协调器
统一协调各个组件，处理业务逻辑
"""
import time
import logging
from typing import Dict, Any, Optional
from models.system_states import SystemState, StateTransitionEvent
from models.data_models import SystemStateData, ZMQDetectData
from .state_manager import state_manager
from .state_handlers import state_handler_factory
import cv2
import base64
class SystemCoordinator:
    """系统协调器"""
    
    def __init__(self):
        """初始化系统协调器"""
        self.logger = logging.getLogger(__name__)
        self.state_manager = state_manager
        self.handler_factory = state_handler_factory
        self.websocket_manager = None
        self.frontend_connected = False
        self.context = {
            "current_time": time.time(),
            "session_active": False
        }
        
    def set_websocket_manager(self, websocket_manager):
        """设置WebSocket管理器引用"""
        self.websocket_manager = websocket_manager
        self.logger.info("WebSocket管理器引用已设置")
    
    def set_frontend_connected(self, connected: bool):
        """设置前端连接状态"""
        self.frontend_connected = connected
        self.logger.info(f"前端连接状态更新: {connected}")
        
    
    def handle_zmq_data(self, camera_frame, detect_data):
        """统一处理ZMQ数据 - 同时接收camera和pose数据"""
        try:
            # 更新上下文时间
            self.context["current_time"] = time.time()
            # 获取当前状态处理器
            current_state = self.state_manager.get_current_state()
            handler = self.handler_factory.get_handler(current_state)
            if not handler:
                self.logger.warning(f"未找到状态处理器: {current_state.value}")
                return
            # 处理数据
            result = handler.handle_data(detect_data, self.context)
            # 构建状态数据
            if(result == None):
                return
            state_data = result.get("state_data")
            if state_data is None:
                state_data = SystemStateData(
                    current_state=current_state,
                    pose_keypoints=detect_data.pose_keypoints if detect_data is not None else [],
                    frame_data=self._encode_frame(camera_frame)
                )
            else:
                # 更新状态数据中的检测数据和帧数据
                state_data.pose_keypoints = detect_data.pose_keypoints if detect_data is not None else []
                state_data.frame_data = self._encode_frame(camera_frame)
         
            result["state_data"] = state_data
            # 发送处理结果
            self._send_data_result("zmq_data", result) 
            # 检查是否需要状态转换
            self._check_state_transition(result)
            
        except Exception as e:
            self.logger.error(f"处理姿态数据失败: {e}")

    def _encode_frame(self, frame: Any) -> str:
        """编码视频帧"""
        success, buffer = cv2.imencode('.jpg', frame, [int(cv2.IMWRITE_JPEG_QUALITY), 80])
        return f'data:image/jpeg;base64,{base64.b64encode(buffer).decode("utf-8")}' if success else "" # type: ignore

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        current_state = self.state_manager.get_current_state()
        return {
            "current_state": current_state.value,
            "frontend_connected": self.frontend_connected,
            "session_active": self.context.get("session_active", False),
            "timestamp": time.time()
        }
    
    def reset_system(self):
        """重置系统"""
        try:
            self.logger.info("重置系统状态")
            # 重置状态机
            self.state_manager.reset_to_waiting()
            # 清理上下文
            self.context.clear()
            self.context.update({
                "current_time": time.time(),
                "session_active": False
            })
            # 重置任务加载器
            from .task.task_loader import task_loader
            task_loader.reset_actions()
            self.logger.info("系统重置完成")
       
        except Exception as e:
            self.logger.error(f"系统重置失败: {e}")
    def start_session(self):
        """开始会话"""
        self.context["session_active"] = True
        self.context["session_start_time"] = time.time()
        self.logger.info("训练会话开始")
        if self.websocket_manager:
            self.websocket_manager.send_message("session_started", {
                "message": "训练会话已开始",
                "start_time": self.context["session_start_time"]
            })
    def stop_session(self):
        """停止会话"""
        self.context["session_active"] = False
        self.logger.info("训练会话停止")
        

    def _send_data_result(self, data_type: str, result: Dict[str, Any]):
        """发送数据处理结果"""
        if not self.websocket_manager:
            return
        try:
            # 发送状态数据
            state_data = result.get("state_data")
            # 发送其他消息类型
            message_type = result.get("websocket_message")
            if message_type:
                self.websocket_manager.send_state_message(message_type, state_data)
                
        except Exception as e:
            self.logger.error(f"发送数据结果失败: {e}")
            

    def handle_preparing_finish(self, data):
        """处理前端发送的preparing_finish事件"""
        try:
            self.logger.info(f"处理preparing_finish事件: {data}")
            # 获取当前状态处理器
            current_state = self.state_manager.get_current_state()
            handler = self.handler_factory.get_handler(current_state)
            if not handler:
                self.logger.warning(f"未找到状态处理器: {current_state.value}")
                return
            # 如果是preparation状态的处理器，调用其preparing_finish方法
            if hasattr(handler, 'handle_preparing_finish'):
                result = handler.handle_preparing_finish(self.context)
                # 发送处理结果
                self._send_data_result("preparing_finish", result)
                # 检查是否需要状态转换
                self._check_state_transition(result)
            else:
                self.logger.warning(f"当前状态处理器不支持preparing_finish事件: {current_state.value}")

        except Exception as e:
            self.logger.error(f"处理preparing_finish事件失败: {e}")

    def handle_clock_introduction_finish(self, data):
        """处理前端发送的介绍倒计时完成事件"""
        try:
            self.logger.info(f"处理clock_introduction_finish事件: {data}")
            # 获取当前状态处理器
            current_state = self.state_manager.get_current_state()
            handler = self.handler_factory.get_handler(current_state)
            if not handler:
                self.logger.warning(f"未找到状态处理器: {current_state.value}")
                return
            # 如果是introduction状态的处理器，调用其handle_introduction_finish方法
            if hasattr(handler, 'handle_introduction_finish'):
                result = handler.handle_introduction_finish(self.context)
                # 发送处理结果
                self._send_data_result("clock_introduction_finish", result)
                # 检查是否需要状态转换
                self._check_state_transition(result)
            else:
                self.logger.warning(f"当前状态处理器不支持clock_introduction_finish事件: {current_state.value}")

        except Exception as e:
            self.logger.error(f"处理clock_introduction_finish事件失败: {e}")

    def _check_state_transition(self, result: Dict[str, Any]):
        """检查并执行状态转换"""
        try:
            trigger_event = result.get("trigger_event")
            if not trigger_event:
                return
            # 执行状态转换
            success = self.state_manager.transition_to(trigger_event, **result)
            current_state = self.state_manager.get_current_state()
            handler = self.handler_factory.get_handler(current_state)
            if handler:
                handler.enter_state(self.context)
            if not success:
                self.logger.warning(f"状态转换失败: {trigger_event}")
        except Exception as e:
            self.logger.error(f"状态转换检查失败: {e}")

# 创建单例实例
system_coordinator = SystemCoordinator()