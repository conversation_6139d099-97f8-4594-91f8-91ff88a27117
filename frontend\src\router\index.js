import { createRouter, createWebHistory } from 'vue-router'
import { useMainStore } from '@/stores/main'


const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/LoginView.vue"),
    meta: {
      title: "用户登录",
      requiresAuth: false,
    },
  },
  {
    path: "/introduction",
    name: "TaskIntroduction",
    component: () => import("@/views/TaskIntroductionView.vue"),
    meta: {
      title: "任务介绍",
      requiresAuth: true,
    },
  },
  {
    path: "/training",
    name: "Training",
    component: () => import("@/views/TrainingView.vue"),
    meta: {
      title: "康复训练",
      requiresAuth: true,
    },
  },
  {
    path: "/report",
    name: "Report",
    component: () => import("@/views/ReportView.vue"),
    meta: {
      title: "训练报告",
      requiresAuth: true,
    },
  },
  {
    path: "/error",
    name: "Error",
    component: () => import("@/views/ErrorView.vue"),
    meta: {
      title: "错误页面",
      requiresAuth: false,
    },
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    redirect: "/login",
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 页面切换时滚动到顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})
// 全局前置守卫 (大大简化版)
router.beforeEach(async (to, from, next) => {
  console.log(`[路由守卫] 拦截导航: 从 ${from.name || 'N/A'} 到 ${to.name}`);

  // 1. 设置页面标题 (保持)
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智能康复系统`;
  }
  const store = useMainStore();
  // 2. 检查WebSocket连接 (简化)
  // 这是守卫的一个好用例：确保核心服务可用。
  if (!store.isConnected && to.name !== 'Error') {
    // 如果未连接，直接导向错误页面。
    // App.vue 中的监听器不会触发，因为状态已经是'start_failed'。
    // 错误页面会负责处理重连逻辑。
    console.warn('[路由守卫] 系统未连接，强制导航到错误页面。');
    next({ name: 'Error' });
    return;
  }
  // 3. 检查认证要求 (保持)
  // 这是路由守卫最经典的应用场景：权限检查。
  if (to.meta.requiresAuth && !store.isUserLoggedIn) {
    console.warn('[路由守卫] 页面需要认证，但用户未登录。重定向到登录页。');
    // 注意：这里重定向到登录页后，App.vue的监听器会发现
    // currentState 和目标路由是匹配的，所以不会产生冲突。
    next({ name: 'Login', query: { redirect: to.fullPath } });
    return;
  }
  
  // 4. 【移除】所有基于 currentState 的重定向逻辑
  // 这部分逻辑现在完全由 App.vue 的集中监听器处理。
  // router.beforeEach 不再关心 currentState 和 allowedStates。
  // 不再需要 getRedirectRouteByState 函数。

  // 5. 【可选保留】特殊页面的前置条件检查
  // 这部分逻辑依然有价值，因为它们检查的是数据的完整性，而不是流程状态。
  if (to.name === 'Training' && (!store.userInfo || !store.actionList || store.actionList.length === 0)) {
    console.warn('[路由守卫] 缺少训练数据，无法进入训练页面。重定向到登录页。');
    next({ name: 'Login' });
    return;
  }

  if (to.name === 'Report' && !store.reportData) { // 检查 reportData 而不是 trainingSession 可能更准确
     console.warn('[路由守卫] 缺少报告数据，无法进入报告页面。重定向到登录页。');
     next({ name: 'Login' });
     return;
  }
  // 如果所有检查都通过，则允许导航。
  console.log('[路由守卫] 所有检查通过，允许导航。');
  next();
});

// ... router.afterEach 和其他辅助函数可以保持，但 getRedirectRouteByState 和 navigateBySystemState 应该被移除或重构
// 因为驱动导航的逻辑已经转移了


export default router
