"""
智能康复系统 - WebSocket管理器
解决SocketIO上下文问题的新实现
"""
import logging
import time
from typing import Optional, Dict, Any
from flask_socketio import SocketIO
from models.data_models import (
    MessageType, SystemStateData,
    DataSerializer
)
from models.system_states import SystemState


class WebSocketManager:
    """WebSocket管理器 - 解决SocketIO上下文问题"""

    def __init__(self):
        """初始化WebSocket管理器"""
        self.socketio: Optional[SocketIO] = None
        self.app = None
        self.logger = logging.getLogger(__name__)

        # 连接管理 - 1对1通信
        self.is_connected = False
        self.current_client_id = None

        # 系统协调器引用
        self.system_coordinator = None
        self.zmq_receiver = None

        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'connection_count': 0
        }
        self.logger.info("WebSocket管理器初始化完成")
    
    def initialize(self, app, socketio: SocketIO):
        """初始化WebSocket管理器"""
        self.app = app
        self.socketio = socketio
        
   
        self._register_events()
        self.logger.info("WebSocket管理器SocketIO初始化完成")
   
    def _register_events(self):
        """注册WebSocket事件"""
        @self.socketio.on('connect')
        def handle_connect():
            """处理客户端连接"""
            try:
                from flask import request
                client_id = getattr(request, 'sid', 'unknown')
                self.current_client_id = client_id
                self.is_connected = True
                self.stats['connection_count'] += 1
                self.logger.info(f"🔗 前端客户端已连接: {client_id}")
                 # 通知ZMQ接收器前端已连接
                if hasattr(self, 'zmq_receiver') and self.zmq_receiver:
                    self.zmq_receiver.set_frontend_connected(True)
                # 启动ZMQ数据处理任务（仅启动一次）
                if not hasattr(self.socketio, '_zmq_task_started'):
                    self.socketio.start_background_task(self._zmq_data_processor)
                    self.socketio._zmq_task_started = True
                    self.logger.info("ZMQ数据处理后台任务已启动")
                # # 启动后台消息处理器（仅启动一次）
                # if not is_task_running():
                # 发送system_init事件并转换到waiting状态
                self._send_system_init()

            except Exception as e:
                self.logger.error(f"处理客户端连接失败: {e}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """处理客户端断开"""
            try:
                from flask import request
                client_id = getattr(request, 'sid', 'unknown')
                self.logger.info(f"🔌 前端客户端已断开: {client_id}")
                self.current_client_id = None
                self.is_connected = False

                # 通知ZMQ接收器前端已断开
                if hasattr(self, 'zmq_receiver') and self.zmq_receiver:
                    self.zmq_receiver.set_frontend_connected(False)
            except Exception as e:
                self.logger.error(f"处理客户端断开失败: {e}")

        @self.socketio.on('preparing_finish')
        def handle_preparing_finish(data):
            """处理前端发送的preparing_finish事件"""
            try:
                self.logger.info(f"收到preparing_finish事件: {data}")
                # 通知系统协调器处理preparing_finish事件
                if self.system_coordinator:
                    self.system_coordinator.handle_preparing_finish(data)
                else:
                    self.logger.warning("系统协调器未初始化，无法处理preparing_finish事件")
            except Exception as e:
                self.logger.error(f"处理preparing_finish事件失败: {e}")

        @self.socketio.on('clock_introduction_finish')
        def handle_clock_introduction_finish(data):
            """处理前端发送的介绍倒计时完成事件"""
            try:
                self.logger.info(f"收到clock_introduction_finish事件: {data}")
                # 通知系统协调器处理clock_introduction_finish事件
                if self.system_coordinator:
                    self.system_coordinator.handle_clock_introduction_finish(data)
                else:
                    self.logger.warning("系统协调器未初始化，无法处理clock_introduction_finish事件")
            except Exception as e:
                self.logger.error(f"处理clock_introduction_finish事件失败: {e}")

    def _zmq_data_processor(self):
        """ZMQ数据处理后台任务"""
        self.logger.info("ZMQ数据处理任务开始运行")
        try:
            # 启动ZMQ接收器的数据处理循环
            if hasattr(self, 'zmq_receiver') and self.zmq_receiver:
                self.zmq_receiver.start_data_processing()
            else:
                # 如果没有zmq_receiver引用，从全局导入
                from ..zmq.zmq_receiver import zmq_receiver
                zmq_receiver.start_data_processing()
        except Exception as e:
            self.logger.error(f"ZMQ数据处理任务异常: {e}")

     
    def send_state_message(self, message_type: MessageType, state_data: SystemStateData, target_client_id: Optional[str] = None):
        """发送状态消息 - 使用专门的消息发送器"""
        try:
            if not self.is_connected:
                self.logger.debug("前端未连接，跳过消息发送")
                return
            # 序列化消息数据
            message_data = DataSerializer.websocket_message_to_dict(state_data)
            self.logger.info(f"📤 准备发送消息: {message_type.value}")
            #直接在当前线程中发送（如果在Flask应用上下文中）
            try:
                if self.app:
                    with self.app.app_context():
                        self.socketio.emit(message_type.value, message_data, namespace='/')
                        self.stats['messages_sent'] += 1
                        return
            except Exception as e:
                self.logger.warning(f"直接发送失败，尝试其他方法: {e}")
          
        except Exception as e:
            self.logger.error(f"发送状态消息失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
    def set_system_coordinator(self, system_coordinator):
        """设置系统协调器引用 - 兼容原有接口"""
        self.system_coordinator = system_coordinator
        self.logger.info("系统协调器已连接到WebSocket管理器")

    def get_current_state(self):
        """获取当前系统状态"""
        if self.system_coordinator:
            return self.system_coordinator.state_manager.get_current_state()
        return SystemState.IDLE

    def _send_system_init(self):
        """发送system_init事件并转换到waiting状态"""
        try:
            from models.system_states import SystemState, MessageType
            from models.data_models import SystemStateData
            # 创建system_init状态数据
            state_data = SystemStateData(
                current_state=SystemState.WAITING,
                message="系统初始化完成，进入等待状态"
            )
            # 发送system_init消息
            self.send_state_message(MessageType.SYSTEM_INIT, state_data)
            self.logger.info("已发送system_init事件")

        except Exception as e:
            self.logger.error(f"发送system_init事件失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'current_state': self.get_current_state(),
            'is_connected': self.is_connected,
            'current_client_id': self.current_client_id
        }

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
