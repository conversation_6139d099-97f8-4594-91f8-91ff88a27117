# src/algorithms/evaluators/base_evaluator.py
from abc import ABC, abstractmethod
from models.data_models import ActionInfo
# src/algorithms/evaluators/base_evaluator.py
from typing import List, Dict, Any, Tuple # 1. 导入 Tuple

class BaseActionEvaluator(ABC):
    """动作评估器的抽象基类，定义了所有评估器必须实现的接口。"""
    def __init__(self, action_info: ActionInfo):
        self.action_info = action_info
        self.kps = None

    def evaluate(self, keypoints: List[List[float]]) -> Dict[str, Any]:
        """
        评估一帧的姿态。这是外部调用的主方法。
        返回包含 'recognized', 'score', 'feedback' 的字典。
        """
        self.kps = keypoints
        
        recognized, message = self.recognize()
        if not recognized:
            return {"recognized": False, "score": 0, "feedback": [message], "details": {}}
        
        score, details = self.score()
        feedback = self.generate_feedback(score, details)
        
        return {"recognized": True, "score": score, "feedback": feedback, "details": details}

    @abstractmethod
    def recognize(self) -> Tu<PERSON>[bool, str]: # 2. 修改类型提示
        """
        识别动作是否正在进行。
        :return: (是否识别, 消息) 的元组
        """
        pass

    @abstractmethod
    def score(self) -> Tuple[float, Dict[str, Any]]: # 3. 修改类型提示
        """
        对已识别的动作进行评分。
        :return: (总分, 评分细节) 的元组
        """
        pass

    @abstractmethod
    def generate_feedback(self, score: float, details: Dict[str, Any]) -> List[str]:
        """
        根据分数和细节生成反馈。
        :return: 反馈消息列表
        """
        pass
        
    def _get_difficulty_multiplier(self) -> float:
        """根据难度调整评分容忍度"""
        level = self.action_info.difficulty_level
        if level == 'easy': return 1.15
        if level == 'hard': return 0.9
        return 1.0