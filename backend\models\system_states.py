"""
智能康复系统 - 系统状态定义
定义系统状态和状态转换规则
"""
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import time
from .constants import TimeConstants
class SystemState(Enum):
    IDLE="idle"
    """系统状态枚举"""
    WAITING = "waiting"                    # 等待用户识别和登录
    INTRODUCTION = "introduction"          # 用户登录成功后的欢迎和任务介绍
    PREPARATION = "preparation"            # 动作准备阶段，播放引导视频
    TRAINING = "training"                  # 动作训练进行中
    REPORTING = "reporting"                # 训练完成，展示报告
    PAUSE = "pause"                        # 暂停状态（用户离开或身份验证失败）

class StateTransitionEvent(Enum):
    """状态转换事件枚举"""
    # 后端到前端事件
    SYSTEM_INIT = "system_init"                    # 系统初始化，WebSocket连接建立成功
    WAITING_MESSAGE = "waiting_message"            # 等待状态消息，发送检测数据
    LOGIN_SUCCESS = "login_success"                # 用户登录成功

    TRAINING_MESSAGE = "training_message"          # 训练状态消息，发送实时反馈
    ACTION_SWITCH = "action_switch"                # 动作切换
    ACTION_COMPLETED = "action_completed"          # 所有动作完成
    USER_LOST = "user_lost"                        # 用户丢失
    USER_NOT_AUTH = "user_not_auth"                # 用户身份验证失败
    USER_BACK = "user_back"                        # 用户返回
    CLOCK_REPORTING_FINISH = "clock_reporting_finish"  # 报告阶段时钟结束

    # 前端到后端事件
    PREPARING_FINISH = "preparing_finish"          # 准备阶段完成（前端发送）
    CLOCK_INTRODUCTION_FINISH = "clock_introduction_finish"  # 介绍阶段时钟结束

# WebSocket消息类型枚举 - 完整版本
class MessageType(str, Enum):
    SYSTEM_DATA = "system_data"
    """WebSocket消息类型枚举"""
    # 后端到前端消息类型
    SYSTEM_INIT = "system_init"                    # 系统初始化
    WAITING_MESSAGE = "waiting_message"            # 等待状态消息
    LOGIN_SUCCESS = "login_success"                # 登录成功
    
    TRAINING_MESSAGE = "training_message"          # 训练消息
    ACTION_SWITCH = "action_switch"                # 动作切换
    ACTION_COMPLETED = "action_completed"          # 动作完成
    USER_LOST = "user_lost"                        # 用户丢失
    USER_NOT_AUTH = "user_not_auth"                # 用户未认证
    USER_BACK = "user_back"                        # 用户返回
    CLOCK_REPORTING_FINISH = "clock_reporting_finish"  # 报告时钟结束

    # 前端到后端消息类型
    PREPARING_FINISH = "preparing_finish"          # 准备完成
    CLOCK_INTRODUCTION_FINISH = "clock_introduction_finish"  # 介绍时钟结束


@dataclass
class StateTransition:
    """状态转换定义"""
    from_state: SystemState
    to_state: SystemState
    event: StateTransitionEvent
    condition: Optional[str] = None  # 转换条件描述
    timeout: Optional[float] = None  # 超时时间（秒）

@dataclass
class StateContext:
    """状态上下文信息 - 增强版本，支持pause状态恢复"""
    current_state: SystemState
    previous_state: Optional[SystemState] = None
    state_start_time: float = 0.0
    state_data: Optional[Dict[str, Any]] = None
    user_info: Optional[Dict[str, Any]] = None
    current_action: Optional[Dict[str, Any]] = None
    action_list: Optional[List[Dict[str, Any]]] = None
    
    # pause状态恢复机制
    paused_from_state: Optional[SystemState] = None  # 暂停前的状态
    pause_reason: Optional[str] = None               # 暂停原因
    pause_start_time: Optional[float] = None         # 暂停开始时间

    # 时间控制
    introduction_start_time: Optional[float] = None  # 介绍开始时间
    user_lost_start_time: Optional[float] = None     # 用户丢失开始时间
    reporting_start_time: Optional[float] = None     # 报告开始时间

    def __post_init__(self):
        if self.state_data is None:
            self.state_data = {}
        if self.state_start_time == 0.0:
            self.state_start_time = time.time()

    def enter_pause_state(self, reason: str) -> None:
        """进入暂停状态"""
        if self.current_state != SystemState.PAUSE:
            self.paused_from_state = self.current_state
            self.pause_reason = reason
            self.pause_start_time = time.time()
            self.previous_state = self.current_state
            self.current_state = SystemState.PAUSE
            self.state_start_time = time.time()

    def resume_from_pause(self) -> bool:
        """从暂停状态恢复"""
        if self.current_state == SystemState.PAUSE and self.paused_from_state:
            self.previous_state = SystemState.PAUSE
            self.current_state = self.paused_from_state
            self.state_start_time = time.time()
            # 清除暂停相关信息
            self.paused_from_state = None
            self.pause_reason = None
            self.pause_start_time = None
            return True
        return False

    def is_in_pause(self) -> bool:
        """检查是否处于暂停状态"""
        return self.current_state == SystemState.PAUSE

    def get_pause_duration(self) -> float:
        """获取暂停持续时间"""
        if self.pause_start_time:
            return time.time() - self.pause_start_time
        return 0.0

class StateTransitionRules:
    """状态转换规则定义"""
    
    # 定义所有有效的状态转换 - 根据新的状态机需求
    VALID_TRANSITIONS: List[StateTransition] = [
        # 1. None -> waiting (system_init)
        # 注：这个转换通常在WebSocket连接建立时由系统自动处理

        # 2. waiting -> introduction (login_success)
        StateTransition(
            from_state=SystemState.WAITING,
            to_state=SystemState.INTRODUCTION,
            event=StateTransitionEvent.LOGIN_SUCCESS,
            condition="用户认证成功，进入欢迎介绍阶段"
        ),

        # 3. introduction -> preparation (clock_introduction_finish)
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.CLOCK_INTRODUCTION_FINISH,
            condition="介绍阶段30秒时钟结束",
            timeout=TimeConstants.INTRODUCTION_DURATION
        ),

        # 4. preparation -> training (preparing_finish)
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.TRAINING,
            event=StateTransitionEvent.PREPARING_FINISH,
            condition="前端引导视频播放完成"
        ),

        # 5. training -> preparation (action_switch)
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.ACTION_SWITCH,
            condition="切换到下一个动作，重新进入准备阶段"
        ),

        # 6. training -> reporting (action_completed)
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.REPORTING,
            event=StateTransitionEvent.ACTION_COMPLETED,
            condition="所有动作完成，进入报告阶段"
        ),

        # 7. reporting -> waiting (clock_reporting_finish)
        StateTransition(
            from_state=SystemState.REPORTING,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.CLOCK_REPORTING_FINISH,
            condition="报告展示完成或超时，返回等待状态",
            timeout=TimeConstants.REPORTING_TIMEOUT
        ),

        # 8. 暂停相关转换 - introduction/preparation/training -> pause
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT
        ),
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户"
        ),
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT
        ),
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户"
        ),
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT
        ),
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户"
        ),

        # 9. pause -> 恢复到之前状态 (user_back)
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.INTRODUCTION,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到introduction状态"
        ),
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到preparation状态"
        ),
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.TRAINING,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到training状态"
        )
    ]
    
    @classmethod
    def get_valid_transitions_from_state(cls, state: SystemState) -> List[StateTransition]:
        """获取指定状态的所有有效转换"""
        return [t for t in cls.VALID_TRANSITIONS if t.from_state == state]
    
    @classmethod
    def is_valid_transition(cls, from_state: SystemState, to_state: SystemState, 
                          event: StateTransitionEvent) -> bool:
        """检查状态转换是否有效"""
        for transition in cls.VALID_TRANSITIONS:
            if (transition.from_state == from_state and 
                transition.to_state == to_state and 
                transition.event == event):
                return True
        return False
    
    @classmethod
    def get_transition(cls, from_state: SystemState, event: StateTransitionEvent) -> Optional[StateTransition]:
        """根据当前状态和事件获取转换规则"""
        for transition in cls.VALID_TRANSITIONS:
            if transition.from_state == from_state and transition.event == event:
                return transition
        return None

class StateValidator:
    """状态验证器"""
    
    @staticmethod
    def validate_state_context(context: StateContext) -> bool:
        """验证状态上下文的有效性"""
        if not isinstance(context.current_state, SystemState):
            return False
        
        if context.previous_state and not isinstance(context.previous_state, SystemState):
            return False
        
        if context.state_start_time <= 0:
            return False
        
        return True
    
    @staticmethod
    def validate_transition_event(event: StateTransitionEvent, context: StateContext) -> bool:
        """验证转换事件在当前上下文中是否有效"""
        # 检查是否有对应的转换规则
        transition = StateTransitionRules.get_transition(context.current_state, event)
        if not transition:
            return False
        
        # 可以在这里添加更多的业务逻辑验证
        return True

# 状态描述映射
STATE_DESCRIPTIONS = {
    SystemState.WAITING: "等待用户识别和登录",
    SystemState.INTRODUCTION: "用户登录成功后的欢迎和任务介绍",
    SystemState.PREPARATION: "动作准备阶段，播放引导视频",
    SystemState.TRAINING: "动作训练进行中",
    SystemState.REPORTING: "训练完成，展示报告",
    SystemState.PAUSE: "暂停状态（用户离开或身份验证失败）",
}

# 事件描述映射
EVENT_DESCRIPTIONS = {
    StateTransitionEvent.SYSTEM_INIT: "系统初始化，WebSocket连接建立成功",
    StateTransitionEvent.WAITING_MESSAGE: "等待状态消息，发送检测数据",
    StateTransitionEvent.LOGIN_SUCCESS: "用户登录成功",
    StateTransitionEvent.CLOCK_INTRODUCTION_FINISH: "介绍阶段时钟结束",
    StateTransitionEvent.TRAINING_MESSAGE: "训练状态消息，发送实时反馈",
    StateTransitionEvent.ACTION_SWITCH: "动作切换",
    StateTransitionEvent.ACTION_COMPLETED: "所有动作完成",
    StateTransitionEvent.USER_LOST: "用户丢失",
    StateTransitionEvent.USER_NOT_AUTH: "用户身份验证失败",
    StateTransitionEvent.USER_BACK: "用户返回",
    StateTransitionEvent.CLOCK_REPORTING_FINISH: "报告阶段时钟结束",
    StateTransitionEvent.PREPARING_FINISH: "准备阶段完成（前端发送）"
}
