"""
智能康复系统 - 常量定义
定义系统中使用的所有常量
"""
# 时间控制常量
class TimeConstants:
    """时间控制常量定义"""
    INTRODUCTION_DURATION = 30.0      # 介绍阶段持续时间（秒）
    USER_LOST_TIMEOUT = 3.0           # 用户丢失检测超时（秒）
    USER_NOT_AUTH_TIMEOUT = 3.0        # 用户身份验证超时（秒）
    USER_DETECTION_TIMEOUT = 5.0      # 用户检测超时（秒）
    REPORTING_TIMEOUT = 30.0          # 报告阶段超时（秒）
# RTMPose关键点映射
class KeyPointMapping:
    """RTMPose 133个关键点映射"""
    
    # 身体姿态关键点 (0-16)
    NOSE = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_EAR = 3
    RIGHT_EAR = 4
    LEFT_SHOULDER = 5
    RIGHT_SHOULDER = 6
    LEFT_ELBOW = 7
    RIGHT_ELBOW = 8
    LEFT_WRIST = 9
    RIGHT_WRIST = 10
    LEFT_HIP = 11
    RIGHT_HIP = 12
    LEFT_KNEE = 13
    RIGHT_KNEE = 14
    LEFT_ANKLE = 15
    RIGHT_ANKLE = 16
    
    # 脚部关键点 (17-22)
    LEFT_BIG_TOE = 17
    LEFT_SMALL_TOE = 18
    LEFT_HEEL = 19
    RIGHT_BIG_TOE = 20
    RIGHT_SMALL_TOE = 21
    RIGHT_HEEL = 22
    
    # 面部关键点 (23-90) - 这里只列出主要的
    FACE_START = 23
    FACE_END = 90
    
    # 左手关键点 (91-111)
    LEFT_HAND_START = 91
    LEFT_HAND_END = 111
    LEFT_HAND_WRIST = 91
    LEFT_HAND_THUMB_1 = 92
    LEFT_HAND_THUMB_2 = 93
    LEFT_HAND_THUMB_3 = 94
    LEFT_HAND_THUMB_4 = 95
    LEFT_HAND_INDEX_1 = 96
    LEFT_HAND_INDEX_2 = 97
    LEFT_HAND_INDEX_3 = 98
    LEFT_HAND_INDEX_4 = 99
    LEFT_HAND_MIDDLE_1 = 100
    LEFT_HAND_MIDDLE_2 = 101
    LEFT_HAND_MIDDLE_3 = 102
    LEFT_HAND_MIDDLE_4 = 103
    LEFT_HAND_RING_1 = 104
    LEFT_HAND_RING_2 = 105
    LEFT_HAND_RING_3 = 106
    LEFT_HAND_RING_4 = 107
    LEFT_HAND_PINKY_1 = 108
    LEFT_HAND_PINKY_2 = 109
    LEFT_HAND_PINKY_3 = 110
    LEFT_HAND_PINKY_4 = 111
    
    # 右手关键点 (112-132)
    RIGHT_HAND_START = 112
    RIGHT_HAND_END = 132
    RIGHT_HAND_WRIST = 112
    RIGHT_HAND_THUMB_1 = 113
    RIGHT_HAND_THUMB_2 = 114
    RIGHT_HAND_THUMB_3 = 115
    RIGHT_HAND_THUMB_4 = 116
    RIGHT_HAND_INDEX_1 = 117
    RIGHT_HAND_INDEX_2 = 118
    RIGHT_HAND_INDEX_3 = 119
    RIGHT_HAND_INDEX_4 = 120
    RIGHT_HAND_MIDDLE_1 = 121
    RIGHT_HAND_MIDDLE_2 = 122
    RIGHT_HAND_MIDDLE_3 = 123
    RIGHT_HAND_MIDDLE_4 = 124
    RIGHT_HAND_RING_1 = 125
    RIGHT_HAND_RING_2 = 126
    RIGHT_HAND_RING_3 = 127
    RIGHT_HAND_RING_4 = 128
    RIGHT_HAND_PINKY_1 = 129
    RIGHT_HAND_PINKY_2 = 130
    RIGHT_HAND_PINKY_3 = 131
    RIGHT_HAND_PINKY_4 = 132
    
# 动作关键点映射
class ActionKeyPoints:
    """各种康复动作所需的关键点"""
    # 摸肩膀动作
    SHOULDER_TOUCH_LEFT = [
        KeyPointMapping.RIGHT_SHOULDER,
        KeyPointMapping.RIGHT_ELBOW,
        KeyPointMapping.RIGHT_WRIST,
        KeyPointMapping.LEFT_SHOULDER
    ] + list(range(KeyPointMapping.RIGHT_HAND_START, KeyPointMapping.RIGHT_HAND_START + 5))
    
    SHOULDER_TOUCH_RIGHT = [
        KeyPointMapping.LEFT_SHOULDER,
        KeyPointMapping.LEFT_ELBOW,
        KeyPointMapping.LEFT_WRIST,
        KeyPointMapping.RIGHT_SHOULDER
    ] + list(range(KeyPointMapping.LEFT_HAND_START, KeyPointMapping.LEFT_HAND_START + 5))
    
    # 手臂上抬动作
    ARM_RAISE_LEFT = [
        KeyPointMapping.LEFT_SHOULDER,
        KeyPointMapping.LEFT_ELBOW,
        KeyPointMapping.LEFT_WRIST
    ]
    
    ARM_RAISE_RIGHT = [
        KeyPointMapping.RIGHT_SHOULDER,
        KeyPointMapping.RIGHT_ELBOW,
        KeyPointMapping.RIGHT_WRIST
    ]
    
    # 对指动作
    FINGER_TOUCH_LEFT = [
        KeyPointMapping.LEFT_WRIST
    ] + list(range(KeyPointMapping.LEFT_HAND_START, KeyPointMapping.LEFT_HAND_END + 1))
    
    FINGER_TOUCH_RIGHT = [
        KeyPointMapping.RIGHT_WRIST
    ] + list(range(KeyPointMapping.RIGHT_HAND_START, KeyPointMapping.RIGHT_HAND_END + 1))
    
    # 手掌翻转动作
    PALM_FLIP_LEFT = [
        KeyPointMapping.LEFT_WRIST
    ] + list(range(KeyPointMapping.LEFT_HAND_START, KeyPointMapping.LEFT_HAND_END + 1))
    
    PALM_FLIP_RIGHT = [
        KeyPointMapping.RIGHT_WRIST
    ] + list(range(KeyPointMapping.RIGHT_HAND_START, KeyPointMapping.RIGHT_HAND_END + 1))

# 系统配置常量
class SystemConfig:
    """系统配置常量"""
    
    # ZMQ配置
    ZMQ_DETECT_PORT = 6070
    ZMQ_CAMERA_PORT = 6080
    ZMQ_TIMEOUT_MS = 100
    
    # WebSocket配置
    WEBSOCKET_PORT = 5000
    WEBSOCKET_HEARTBEAT_INTERVAL = 30  # 秒
    
    
    # 动作识别配置
    MIN_CONFIDENCE = 0.3
    MIN_ACTION_CONFIDENCE = 0.5
    SCORE_THRESHOLD = 60  # 及格分数
    MAX_SCORE = 100
    
    # 训练配置
    DEFAULT_REST_TIME = 30  # 秒
    MAX_TRAINING_TIME = 3600  # 1小时
    
    # 数据存储配置
    MAX_HISTORY_DAYS = 30
    MAX_SESSION_COUNT = 1000

# 错误代码
class ErrorCodes:
    """系统错误代码"""
    
    # 数据相关错误
    DATA_INVALID_FORMAT = 1001
    DATA_MISSING_FIELD = 1002
    DATA_VALIDATION_FAILED = 1003
    
    # ZMQ相关错误
    ZMQ_CONNECTION_FAILED = 2001
    ZMQ_TIMEOUT = 2002
    ZMQ_DATA_CORRUPTED = 2003
    
    # WebSocket相关错误
    WEBSOCKET_CONNECTION_FAILED = 3001
    WEBSOCKET_MESSAGE_FAILED = 3002
    
    # 动作识别错误
    ACTION_RECOGNITION_FAILED = 4001
    ACTION_KEYPOINTS_MISSING = 4002
    ACTION_CONFIDENCE_LOW = 4003
    
    # 用户相关错误
    USER_NOT_FOUND = 5001
    USER_AUTHENTICATION_FAILED = 5002
    
    # 系统错误
    SYSTEM_INITIALIZATION_FAILED = 9001
    SYSTEM_RESOURCE_EXHAUSTED = 9002
