# src/algorithms/evaluators/shoulder_touch.py
import numpy as np
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math

class ShoulderTouchEvaluator(BaseActionEvaluator):
    def recognize(self) :
        side = self.action_info.side
        kps = self.kps
        
        hand, target_shoulder, shoulder_l, shoulder_r = pose_math.get_keypoints(kps, [
            K.LEFT_WRIST if side == "left" else K.RIGHT_WRIST,
            K.RIGHT_SHOULDER if side == "left" else K.LEFT_SHOULDER,
            K.LEFT_SHOULDER, K<PERSON>RIGHT_SHOULDER
        ])

        if not all([hand, target_shoulder, shoulder_l, shoulder_r]):
            return False, "请确保双肩和手腕在画面中"
        
        shoulder_width = pose_math.calculate_distance(shoulder_l, shoulder_r)
        if shoulder_width < 1e-5: return False, "肩部关键点异常"
        
        distance = pose_math.calculate_distance(hand, target_shoulder)
        if distance / shoulder_width < 0.2:
            return True, ""
        return False, "请将手靠近对侧肩膀"

    def score(self):
        multiplier = self._get_difficulty_multiplier()
        
        # 评分维度1: 身体稳定性 (躯干倾斜度)
        shoulder_l, shoulder_r, hip_l, hip_r = pose_math.get_keypoints(self.kps, 
            [K.LEFT_SHOULDER, K.RIGHT_SHOULDER, K.LEFT_HIP, K.RIGHT_HIP])
        
        stability_score = 100
        if all([shoulder_l, shoulder_r, hip_l, hip_r]):
            shoulder_mid = (shoulder_l[:2] + shoulder_r[:2]) / 2
            hip_mid = (hip_l[:2] + hip_r[:2]) / 2
            torso_vec = shoulder_mid - hip_mid
            # 与垂直线的夹角
            vertical_vec = np.array([0, -1]) 
            angle_with_vertical = np.degrees(np.arccos(np.dot(torso_vec, vertical_vec) / np.linalg.norm(torso_vec)))
            
            # 倾斜角度越小，得分越高
            stability_score = np.interp(angle_with_vertical, [10, 30], [100, 0])
        
        final_score = min(100, stability_score * multiplier)
        details = {"stability_score_raw": stability_score}
        return final_score, details

    def generate_feedback(self, score: float, details: dict) -> list:
        if score >= 90:
            return ["动作标准，身体很稳定！"]
        if score < 75 and details.get("stability_score_raw", 100) < 80:
            return ["动作基本完成，但请尽量保持上半身挺直，不要晃动。"]
        return ["完成得不错！"]