# src/algorithms/evaluators/arm_raise.py
import numpy as np
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math

class ArmRaiseEvaluator(BaseActionEvaluator):
    def recognize(self) :
        side = self.action_info.side
        kps = self.kps
        
        shoulder, elbow, wrist = pose_math.get_keypoints(kps, [
            K.LEFT_SHOULDER if side == "left" else K.RIGHT_SHOULDER,
            K.LEFT_ELBOW if side == "left" else K.RIGHT_ELBOW,
            K.LEFT_WRIST if side == "left" else K.RIGHT_WRIST
        ])
        
        if not all([shoulder, elbow, wrist]):
            return False, "请确保手臂在画面中"

        # 只要手腕在肩膀上方，就认为在做这个动作
        if wrist[1] < shoulder[1]:
            return True, ""
        return False, "请将手臂向上举起"

    def score(self) :
        side = self.action_info.side
        kps = self.kps
        multiplier = self._get_difficulty_multiplier()
        
        shoulder, elbow, wrist, hip = pose_math.get_keypoints(kps, [
            K.LEFT_SHOULDER if side == "left" else K.RIGHT_SHOULDER,
            K.LEFT_ELBOW if side == "left" else K.RIGHT_ELBOW,
            K.LEFT_WRIST if side == "left" else K.RIGHT_WRIST,
            K.LEFT_HIP if side == "left" else K.RIGHT_HIP
        ])
        
        if not all([shoulder, elbow, wrist, hip]):
            return 0, {}

        # 评分维度1: 手臂伸直程度
        straightness_angle = pose_math.calculate_angle(shoulder, elbow, wrist)
        straightness_score = np.interp(straightness_angle, [140, 170], [0, 50])
        
        # 评分维度2: 上举高度
        raise_angle = pose_math.calculate_angle(hip, shoulder, wrist)
        target_angles = {'easy': [60, 150], 'medium': [80, 160], 'hard': [90, 170]}
        angles = target_angles.get(self.action_info.difficulty_level, [80, 160])
        raise_score = np.interp(raise_angle, angles, [0, 50])

        total_score = straightness_score + raise_score
        final_score = min(100, total_score * multiplier)
        
        details = {
            "straightness_angle": straightness_angle,
            "raise_angle": raise_angle,
            "straightness_score_raw": straightness_score,
            "raise_score_raw": raise_score
        }
        return final_score, details

    def generate_feedback(self, score: float, details: dict) -> list:
        feedback = []
        if score >= 95:
            return ["动作完美！"]
        if score >= 80:
            feedback.append("完成得很好！")
        else:
            feedback.append("加油，请注意调整。")

        if details.get("straightness_score_raw", 50) < 40:
            feedback.append("提示：手臂再伸直一些。")
        if details.get("raise_score_raw", 50) < 40:
            feedback.append(f"提示：上举高度不足，目标角度约 {self.action_info.difficulty_level} 度。")
        return feedback