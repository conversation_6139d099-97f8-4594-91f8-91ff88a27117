# 算法模块
# src/algorithms/evaluators/__init__.py

# 导入基类，以便其他地方可能需要引用
from .base_evaluator import BaseActionEvaluator

# 导入所有具体的评估器类，这样工厂就能发现它们
from .arm_raise import ArmRaiseEvaluator
from .shoulder_touch import ShoulderTouchEvaluator
from .finger_touch import FingerTouchEvaluator
from .palm_flip import PalmFlipEvaluator

# (可选) 定义 __all__，控制 `from .evaluators import *` 的行为
__all__ = [
    "BaseActionEvaluator",
    "ArmRaiseEvaluator",
    "ShoulderTouchEvaluator",
    "FingerTouchEvaluator",
    "PalmFlipEvaluator",
]