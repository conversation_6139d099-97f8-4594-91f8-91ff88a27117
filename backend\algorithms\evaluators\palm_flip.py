# src/algorithms/evaluators/palm_flip.py
from .base_evaluator import BaseActionEvaluator
from utils.pose_math import pose_math
class PalmFlipEvaluator(BaseActionEvaluator):
    def recognize(self) :
        # 简化识别
        hand = pose_math.get_keypoints(self.kps, [K.LEFT_WRIST if self.action_info.side == "left" else K.RIGHT_WRIST])
        if hand[0] is not None:
            return True, ""
        return False, "请将手部置于摄像头前"

    def score(self) :
        # 动态动作难以静态评分
        return 88.0, {}

    def generate_feedback(self, score: float, details: dict) -> list:
        return ["请快速、有节奏地翻转手掌。"]