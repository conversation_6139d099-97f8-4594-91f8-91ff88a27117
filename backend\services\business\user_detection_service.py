"""
智能康复系统 - 用户检测服务
提供通用的用户检测和身份验证功能
"""
import time
import logging
from typing import Dict, Any, Optional
from models.data_models import ZMQDetectData, UserInfo
from models.constants import TimeConstants
from .task.task_loader import task_loader

class UserDetectionService:
    """用户检测服务 - 提供通用的用户检测和身份验证功能"""
    
    def __init__(self):
        """初始化用户检测服务"""
        self.logger = logging.getLogger(__name__)
        
        # 用户检测相关
        self.detection_count = 0
        self.detection_threshold = 3  # 连续检测3次才认为用户稳定
        self.last_patient_id = None
        
        # 用户丢失检测相关
        self.user_lost_start_time = None
        self.user_not_auth_start_time = None
        # 身份验证相关
        self.last_authenticated_user_id = None
        
        self.logger.info("用户检测服务初始化完成")
    
    def detect_and_authenticate_user(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测并认证用户（用于waiting状态）
        
        Args:
            pose_data: 姿态检测数据
            context: 上下文信息
            
        Returns:
            Dict: 检测和认证结果
        """
        try:
            patient_id = pose_data.patient_id
            # 检查是否检测到有效用户ID
            if not patient_id or patient_id == "unknown":
                self.detection_count = 0
                self.last_patient_id = None
                return {
                    "success": True,
                    "user_detected": False,
                    "user_authenticated": False,
                    "message": "未检测到有效用户"
                }
            # 检查是否为连续的同一用户
            if patient_id == self.last_patient_id:
                self.detection_count += 1
            else:
                self.detection_count = 1
                self.last_patient_id = patient_id
            self.logger.debug(f"用户检测: {patient_id}, 连续次数: {self.detection_count}")
            # 检查是否达到检测阈值
            if self.detection_count >= self.detection_threshold:
                self.logger.info(f"用户检测成功，开始认证: {patient_id}")
                # 进行用户认证
                user_info = self._authenticate_user(patient_id)
                if user_info:
                    # 加载用户任务列表
                    action_list = self._load_user_actions(patient_id) 
                    # 更新上下文
                    update_data = {
                        "detected_patient_id": patient_id,
                        "detection_time": pose_data.timestamp,
                        "user_info": user_info,
                        "action_list": action_list,
                        "current_action":action_list[task_loader.current_action_index]
                    }
                    context.update(update_data)
                    # 记录认证成功的用户
                    self.last_authenticated_user_id = patient_id
                    return {
                        "success": True,
                        "user_detected": True,
                        "user_authenticated": True,
                        "message": f"用户认证成功: {user_info.name}",
                        "user_info": user_info,
                        "action_list": action_list
                    }
                else:
                    # 认证失败，重置检测
                    self.detection_count = 0
                    self.last_patient_id = None
                    return {
                        "success": True,
                        "user_detected": True,
                        "user_authenticated": False,
                        "message": f"用户认证失败: {patient_id}"
                    }
            
            return {
                "success": True,
                "user_detected": True,
                "user_authenticated": False,
                "message": f"正在检测用户: {patient_id} ({self.detection_count}/{self.detection_threshold})",
                "detection_progress": self.detection_count / self.detection_threshold
            }
            
        except Exception as e:
            self.logger.error(f"用户检测和认证失败: {e}")
            return {
                "success": False,
                "user_detected": False,
                "user_authenticated": False,
                "message": f"用户检测处理失败: {str(e)}"
            }
    


    def check_user(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查用户是否在画面中且是登录用户（用于非waiting状态）
        
        Args:
            pose_data: 姿态检测数据
            context: 上下文信息
            
        Returns:
            Dict: 用户在场且是登录用户检测结果
        """
        try:
            patient_id = pose_data.patient_id
            # 只检查用户是否在画面中，不检查身份
            if not patient_id or patient_id == "unknown":
                if self.user_lost_start_time is None:
                    self.user_lost_start_time = time.time()
                    self.logger.debug("开始检测用户丢失")
                else:
                    lost_duration = time.time() - self.user_lost_start_time
                    self.logger.debug(f"用户丢失时长: {lost_duration:.1f}秒")
                    if lost_duration > TimeConstants.USER_LOST_TIMEOUT:
                        self.logger.warning(f"用户丢失超过{TimeConstants.USER_LOST_TIMEOUT}秒")
                        return {
                            "success": True,
                            "should_pause": True,
                            "pause_reason": "user_lost",
                            "message": f"用户消失超过{TimeConstants.USER_LOST_TIMEOUT}秒"
                        }
            else:
                # 检测到用户，重置丢失时间
                if self.user_lost_start_time is not None:
                    self.logger.debug("用户已返回，重置丢失计时")
                    self.user_lost_start_time = None
            if patient_id != context.get("patient_id"):
                if self.user_not_auth_start_time is None:
                    self.user_not_auth_start_time = time.time()
                    self.logger.debug("开始检测用户不匹配")
                else:
                    lost_duration = time.time() - self.user_not_auth_start_time
                    self.logger.debug(f"用户不匹配时长: {lost_duration:.1f}秒")
                    if lost_duration > TimeConstants.USER_NOT_AUTH_TIMEOUT:
                        self.logger.warning(f"用户不匹配超过{TimeConstants.USER_NOT_AUTH_TIMEOUT}秒")
                        return {
                            "success": True,
                            "should_pause": True,
                            "pause_reason": "user_not_auth",
                            "message": f"用户不匹配超过{TimeConstants.USER_NOT_AUTH_TIMEOUT}秒"
                        }
            else:
                # 检测到用户，重置丢失时间
                if self.user_not_auth_start_time is not None:
                    self.logger.debug("用户已返回，重置丢失计时")
                    self.user_not_auth_start_time = None
            return {
                "success": True,
                "should_pause": False,
                "message": "用户在画面中且是登录用户"
            }
            
        except Exception as e:
            self.logger.error(f"用户在场检测失败: {e}")
            return {
                "success": False,
                "should_pause": False,
                "message": f"用户在场检测失败: {str(e)}"
            }
    
    def reset_detection(self):
        """重置检测状态"""
        self.detection_count = 0
        self.last_patient_id = None
        self.user_lost_start_time = None
        self.logger.debug("用户检测状态已重置")
    
    def _authenticate_user(self, patient_id: str) -> Optional[UserInfo]:
        """用户认证（简化实现）"""
        # TODO: 实际应该调用用户服务进行认证
        # 这里简化处理，假设所有检测到的用户都是有效的
        return UserInfo(
            patient_id=patient_id,
            name=f"用户{patient_id}",  # 实际应该从数据库获取
            age=None,
            gender=None,
            last_login=None
        )
    
    def _load_user_actions(self, patient_id: str) -> list:
        """加载用户任务列表（简化实现）"""
        # TODO: 实际应该调用任务服务获取用户的训练任务
       
        return task_loader.get_actions_for_user(patient_id)


# 创建单例实例
user_detection_service = UserDetectionService()
