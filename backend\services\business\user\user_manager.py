"""
智能康复系统 - 用户管理服务
实现用户身份验证、任务数据加载和管理功能
"""
import json
import time
import logging
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timezone
from models.data_models import UserInfo, ActionInfo, CurrentAction, TrainingSession
from models.constants import SystemConfig

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        """初始化用户管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 数据文件路径
        self.users_file = Path(__file__).parent.parent.parent.parent / "data" / "users.json"
        self.actions_file = Path(__file__).parent.parent.parent.parent / "data" / "tasks_template.json"
        
        # 内存缓存
        self.users_cache: Dict[str, Dict[str, Any]] = {}
        self.actions_cache: Dict[str, Dict[str, Any]] = {}
        
        # 当前活跃会话
        self.active_sessions: Dict[str, TrainingSession] = {}
        
        # 加载数据
        self._load_users()
        self._load_actions()

        self.logger.info("用户管理器初始化完成")
    
    def _load_users(self):
        """加载用户数据"""
        try:
            if self.users_file.exists():
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.users_cache = data.get('users', {})
                self.logger.info(f"加载用户数据: {len(self.users_cache)} 个用户")
            else:
                self.logger.warning("用户数据文件不存在")
                self.users_cache = {}
        except Exception as e:
            self.logger.error(f"加载用户数据失败: {e}")
            self.users_cache = {}

    def _load_actions(self):
        """加载任务模板数据"""
        try:
            if self.actions_file.exists():
                with open(self.actions_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.actions_cache = data.get('task_templates', {})
                self.logger.info(f"加载任务模板: {len(self.actions_cache)} 个模板")
            else:
                self.logger.warning("任务模板文件不存在")
                self.actions_cache = {}
        except Exception as e:
            self.logger.error(f"加载任务模板失败: {e}")
            self.actions_cache = {}
    
    def _save_users(self):
        """保存用户数据"""
        try:
            # 确保目录存在
            self.users_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'users': self.users_cache,
                'metadata': {
                    'version': '1.0',
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'total_users': len(self.users_cache),
                    'active_users': len([u for u in self.users_cache.values() if u.get('last_login')])
                }
            }
            
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info("用户数据保存成功")
        except Exception as e:
            self.logger.error(f"保存用户数据失败: {e}")
    
    def authenticate_user(self, patient_id: str) -> Optional[UserInfo]:
        """
        用户身份验证
        Args:
            patient_id: 患者ID
        Returns:
            UserInfo: 用户信息，验证失败返回None
        """
        try:
            if patient_id not in self.users_cache:
                self.logger.warning(f"用户不存在: {patient_id}")
                return None
            
            user_data = self.users_cache[patient_id]
            # 更新最后登录时间（转换为ISO格式字符串）
            user_data['last_login'] = datetime.now(timezone.utc).isoformat()
            self._save_users()
            # 将ISO格式字符串转换为datetime对象
            last_login_str = user_data['last_login']
            last_login_datetime = datetime.fromisoformat(last_login_str) if last_login_str else None
            
            # 创建UserInfo对象
            user_info = UserInfo(
                patient_id=user_data['patient_id'],
                name=user_data['name'],
                age=user_data.get('age'),
                gender=user_data.get('gender'),
                last_login=last_login_datetime
            )
            self.logger.info(f"用户验证成功: {patient_id} - {user_data['name']}")
            return user_info
            
        except Exception as e:
            self.logger.error(f"用户验证失败: {e}")
            return None
    
    def get_user_actions(self, patient_id: str) -> List[ActionInfo]:
        """
        获取用户分配的任务
        
        Args:
            patient_id: 患者ID
            
        Returns:
            List[actionInfo]: 任务列表
        """
        try:
            if patient_id not in self.users_cache:
                self.logger.warning(f"用户不存在: {patient_id}")
                return []
            actions = [
                ActionInfo(
                    action_type="shoulder_touch",
                    side="left",
                    difficulty_level="easy",
                    required_keypoints=[5, 6, 7, 8, 9, 10],
                    video_url=None
                ),
                ActionInfo(
                    action_type="arm_raise",
                    side="right",
                    difficulty_level="easy",
                    required_keypoints=[5, 6, 7, 8, 9, 10],
                    video_url=None
                ),
                ActionInfo(
                    action_type="finger_touch",
                    side="left",
                    difficulty_level="easy",
                    required_keypoints=[5, 7, 9],
                    video_url=None
                ),
                ActionInfo(
                    action_type="palm_flip",
                    side="right",
                    difficulty_level="easy",
                    required_keypoints=[6, 8, 10],
                    video_url=None
                )
            ]
            self.logger.info(f"获取用户任务: {patient_id} - {len(actions)} 个任务")
            return actions
            
        except Exception as e:
            self.logger.error(f"获取用户任务失败: {e}")
            return []

   

# 全局用户管理器实例
user_manager = UserManager()
