"""
智能康复系统 - WAITING状态处理器
处理等待用户识别和登录状态的逻辑
根据新状态机需求重构
"""
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import ZMQDetectData, SystemStateData, UserInfo
from . import BaseStateHandler
from ..user_detection_service import user_detection_service

import time

class WaitingHandler(BaseStateHandler):
    """WAITING状态处理器 - 重构版本"""

    def __init__(self):
        """初始化WAITING状态处理器"""
        super().__init__(SystemState.WAITING)
        self.detection_threshold = 3  # 连续检测次数阈值
        self.detection_count = 0
        self.last_patient_id = None
        self.user_auth_service = None  # 用户认证服务，需要注入
    
    def enter_state(self, context: Dict[str, Any]):
        """进入WAITING状态"""
        self.logger.info("系统进入等待用户识别和登录状态")

    def handle_data(self, data: Any, context: Dict[str, Any]):
        """处理WAITING状态下的数据 - 持续发送waiting_message"""
        try:
            # 处理用户检测逻辑
            detection_result = self._handle_user_detection(data, context)
            # 无论是否检测到用户，都要发送waiting_message
            waiting_message_data = self._create_waiting_message(data, context, detection_result)
            # 如果检测到用户并认证成功，触发状态转换
            if detection_result.get("user_authenticated"):
                return {
                    "success": True,
                    "trigger_event": StateTransitionEvent.LOGIN_SUCCESS,
                    "next_state": SystemState.INTRODUCTION,
                    "websocket_message": MessageType.LOGIN_SUCCESS,
                    "state_data": detection_result["state_data"],
                }
            # 未检测到用户或认证失败，只发送waiting_message
            return {
                "success": True,
                "websocket_message": MessageType.WAITING_MESSAGE,
                "state_data": waiting_message_data,
            }
        except Exception as e:
            self.logger.error(f"处理等待状态数据失败: {e}")
            # 错误情况下也要发送状态数据
            error_state_data = SystemStateData(
                current_state=SystemState.WAITING,
                message=f"数据处理失败: {str(e)}",
            )
            return {
                "success": False,
                "websocket_message": MessageType.WAITING_MESSAGE,
                "state_data": error_state_data,
            }
    def _handle_user_detection(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户检测和认证逻辑 - 使用公共用户检测服务"""
        try:
            # 使用公共用户检测服务进行检测和认证
            detection_result = user_detection_service.detect_and_authenticate_user(pose_data, context)
            # 如果用户认证成功，创建状态转换数据
            if detection_result.get("user_authenticated"):
                context.update({"patient_id": detection_result["user_info"].patient_id})
                context.update({"action_list": detection_result["action_list"]})
                user_info = detection_result.get("user_info")
                action_list = detection_result.get("action_list")
                # 创建状态数据
                state_data = SystemStateData(
                    current_state=SystemState.INTRODUCTION,
                    message=f"欢迎回来，{user_info.name if user_info else '用户'}！",
                    user_info=user_info,
                    action_list=action_list or [],
                    current_action=context.get("current_action")
                )
                return {
                    "success": True,
                    "user_authenticated":True,
                    "state_data": state_data,
                }
            # 返回检测结果
            return detection_result

        except Exception as e:
            self.logger.error(f"处理用户检测失败: {e}")
            return {
                "success": False,
                "message": f"用户检测处理失败: {str(e)}",
                "user_detected": False,
                "user_authenticated": False
            }
    
    def _create_waiting_message(self, pose_data: ZMQDetectData, context: Dict[str, Any], detection_result: Dict[str, Any]) -> SystemStateData:
        """创建waiting_message数据"""
        return SystemStateData(
            current_state=SystemState.WAITING,
            message="等待用户识别中...",
            face_box = pose_data.face_bbox,
            patient_id = pose_data.patient_id
        )

    def exit_state(self, context: Dict[str, Any]):
        """退出WAITING状态"""
        self.logger.info("系统退出等待用户识别和登录状态")

    def reset_detection(self):
        """重置检测状态"""
        self.detection_count = 0
        self.last_patient_id = None
        self.logger.debug("用户检测状态已重置")
