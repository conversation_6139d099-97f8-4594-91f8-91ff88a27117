# 训练页面设计文档

## 概述

训练页面是智能康复系统的核心界面，提供实时的康复训练指导、动作监测和反馈功能。页面采用响应式设计，集成了视频流、姿态检测、动作评估等多项技术。

## 页面布局

### 整体结构
```
┌─────────────────────────────────────────────────────────────┐
│                    训练页面 (TrainingView)                    │
├─────────────┬───────────────────────────────────────────────┤
│             │                上半部分                        │
│   左侧任务   ├─────────────────┬───────────────────────────┤
│   列表栏     │   实时视频流区域   │    标准动作演示区域        │
│             │                │                          │
│             ├─────────────────┴───────────────────────────┤
│             │                下半部分                        │
│             │            实时反馈信息区域                     │
└─────────────┴───────────────────────────────────────────────┘
```

### 区域详细说明

#### 1. 左侧任务列表栏 (320px 宽度)
- **头部信息区域**
  - 训练任务标题
  - 系统状态标签 (准备中/训练中/已暂停)
  - 用户信息卡片 (头像、姓名、患者ID)

- **任务列表区域**
  - 动态任务列表，支持滚动
  - 每个任务项显示：
    - 动作名称 (如：摸肩膀(左侧))
    - 动作类型和侧别
    - 完成状态图标
    - 当前任务的进度条

- **底部控制区域**
  - 开始训练按钮 (准备阶段)
  - 暂停/恢复按钮 (训练阶段)
  - 返回首页按钮

#### 2. 实时视频流区域 (flex-1)
- **视频流头部**
  - 标题："实时画面"
  - 连接状态标签
  - 实时帧率显示

- **视频内容区域**
  - VideoStream组件集成
  - 姿态关键点实时叠加
  - 暂停状态遮罩层

#### 3. 标准动作演示区域 (320px 宽度)
- **演示头部**
  - 标题："标准动作"
  - 当前动作名称

- **演示内容**
  - 标准动作视频播放
  - 动作要点说明
  - 实时提示信息

#### 4. 实时反馈信息区域 (256px 高度)
- **反馈头部**
  - 标题："实时反馈"
  - 当前得分和目标得分显示

- **反馈内容**
  - 左侧：训练状态信息
    - 动作状态标签
    - 实时评分进度条
    - 训练计时器
  - 右侧：消息记录
    - 最近5条通知消息
    - 时间戳显示

## 核心功能

### 1. 实时视频流处理
- 集成VideoStream组件
- 支持姿态关键点可视化
- 实时帧率监控
- 连接状态管理

### 2. 动作识别与评估
- 支持4种康复动作：
  - 摸肩膀 (shoulder_touch)
  - 手臂上抬 (arm_raise)
  - 对指训练 (finger_touch)
  - 手掌翻转 (palm_flip)
- 实时动作评分 (0-100分)
- 动作完成度进度跟踪

### 3. 状态管理
- 响应后端状态机：
  - PREPARATION: 准备阶段
  - TRAINING: 训练进行中
  - PAUSE: 暂停状态
- 自动状态转换和页面跳转

### 4. 用户交互
- 任务选择和切换
- 训练控制 (开始/暂停/恢复)
- 实时反馈展示
- 消息通知系统

## 技术实现

### 组件架构
```javascript
TrainingView.vue
├── VideoStream.vue (视频流组件)
├── Element Plus UI组件
│   ├── el-tag (状态标签)
│   ├── el-button (控制按钮)
│   ├── el-progress (进度条)
│   └── el-icon (图标)
└── TailwindCSS (样式框架)
```

### 状态管理
- 使用Pinia主store (useMainStore)
- 响应式数据绑定
- 计算属性优化性能

### 关键方法
```javascript
// 动作相关
getActionDisplayName()    // 获取动作显示名称
getActionTypeText()       // 获取动作类型文本
isCurrentAction()         // 判断是否为当前动作
isActionCompleted()       // 判断动作是否完成

// 训练控制
handlePreparingFinish()   // 处理准备完成
togglePause()             // 切换暂停状态
selectAction()            // 选择动作

// 状态显示
getActionStatusType()     // 获取动作状态类型
getActionStatusText()     // 获取动作状态文本
getScoreColor()           // 获取评分颜色

// 时间格式化
formatTrainingTime()      // 格式化训练时间
formatTime()              // 格式化时间戳

// 通知样式
getNotificationBgClass()  // 获取通知背景样式
getNotificationIconClass() // 获取通知图标样式
getNotificationTextClass() // 获取通知文本样式
```

## 响应式设计

### 断点适配
- 桌面端：完整四区域布局
- 平板端：自适应宽度调整
- 移动端：垂直堆叠布局 (通过CSS媒体查询)

### 性能优化
- 计算属性缓存
- 事件防抖处理
- 组件懒加载
- 内存泄漏防护

## 样式特性

### 设计语言
- 现代扁平化设计
- 渐变色背景
- 圆角卡片布局
- 阴影层次感

### 颜色方案
- 主色调：蓝色系 (#3b82f6)
- 成功色：绿色系 (#67c23a)
- 警告色：黄色系 (#e6a23c)
- 错误色：红色系 (#f56c6c)

### 动画效果
- 页面过渡动画
- 按钮悬停效果
- 进度条动画
- 通知弹出动画

## 测试覆盖

### 单元测试
- 组件渲染测试
- 用户交互测试
- 状态变化测试
- 事件处理测试

### 集成测试
- 路由导航测试
- Store状态测试
- WebSocket通信测试

## 使用说明

### 页面访问
1. 用户必须先登录系统
2. 系统状态必须为 'preparation' 或 'training'
3. 必须有有效的用户信息和任务列表

### 操作流程
1. **准备阶段**：观看标准动作演示，点击"开始训练"
2. **训练阶段**：按照指导完成动作，查看实时反馈
3. **暂停恢复**：可随时暂停和恢复训练
4. **完成训练**：系统自动跳转到报告页面

### 注意事项
- 确保摄像头权限已开启
- 保持良好的网络连接
- 在充足光线环境下进行训练
- 遵循动作指导，避免受伤

## 扩展性

### 新增动作类型
1. 在动作映射表中添加新类型
2. 更新后端动作识别算法
3. 添加对应的标准动作视频

### 界面定制
1. 修改TailwindCSS配置
2. 调整组件布局参数
3. 更新主题色彩方案

### 功能增强
1. 添加语音指导功能
2. 集成AR/VR支持
3. 增加多人协作训练
4. 添加训练数据分析
