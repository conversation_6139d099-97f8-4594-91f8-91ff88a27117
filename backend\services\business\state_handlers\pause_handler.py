"""
智能康复系统 - PAUSE状态处理器
处理暂停状态和恢复逻辑
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import ZMQDetectData, SystemStateData
from . import BaseStateHandler

class PauseHandler(BaseStateHandler):
    """PAUSE状态处理器"""
    
    def __init__(self):
        """初始化PAUSE状态处理器"""
        super().__init__(SystemState.PAUSE)
        self.pause_start_time = None
        self.paused_from_state = None
        self.pause_reason = None
        self.expected_user_id = None
    
    def enter_state(self, context: Dict[str, Any]):
        """进入PAUSE状态"""
        self.logger.info("系统进入暂停状态")
        self.pause_start_time = time.time()
        
        # 记录暂停前的状态信息
        self.paused_from_state = context.get("previous_state")
        self.pause_reason = context.get("pause_reason", "unknown")
        
        # 获取期望的用户ID
        user_info = context.get("user_info")
        if user_info:
            self.expected_user_id = user_info.patient_id
        
        self.logger.info(f"暂停原因: {self.pause_reason}, 暂停前状态: {self.paused_from_state}")
        
        # 设置暂停信息到上下文
        context.update({
            "pause_start_time": self.pause_start_time,
            "paused_from_state": self.paused_from_state,
            "pause_reason": self.pause_reason
        })
    
    def handle_data(self, data: Any, context: Dict[str, Any]) :
        """处理PAUSE状态下的数据"""
        try:
            # 检查是否可以恢复
            recovery_result = self._check_recovery_conditions(data, context)
            
            if recovery_result["can_resume"]:
                return self._handle_resume(data, context, recovery_result)
                
        except Exception as e:
            self.logger.error(f"处理暂停状态数据失败: {e}")
            

    def _check_recovery_conditions(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查恢复条件"""
        patient_id = pose_data.patient_id
        
        # 检查用户是否回来
        if not patient_id or patient_id == "unknown":
            return {
                "can_resume": False,
                "reason": "用户仍未检测到"
            }
        
        # 检查是否为期望的用户
        if self.expected_user_id and patient_id != self.expected_user_id:
            return {
                "can_resume": False,
                "reason": f"检测到用户 {patient_id}，但期望用户为 {self.expected_user_id}"
            }
        
        # 用户回来了，可以恢复
        return {
            "can_resume": True,
            "reason": f"用户 {patient_id} 已返回",
            "user_id": patient_id
        }
    
    def _handle_resume(self, pose_data: ZMQDetectData, context: Dict[str, Any], recovery_result: Dict[str, Any]) -> Dict[str, Any]:
        """处理恢复逻辑"""
        user_id = recovery_result["user_id"]
        pause_duration = time.time() - self.pause_start_time if self.pause_start_time else 0
        
        self.logger.info(f"用户 {user_id} 返回，恢复到 {self.paused_from_state} 状态，暂停时长: {pause_duration:.1f}秒")
        
        # 确定恢复到的状态
        resume_state = self._determine_resume_state()
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=resume_state,
            message=f"欢迎回来！继续您的训练...",
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.USER_BACK,
            "next_state": resume_state,
            "websocket_message": MessageType.USER_BACK,
            "state_data": state_data,
            "pause_duration": pause_duration,
            "resumed_from": self.paused_from_state
        }
    
    def _determine_resume_state(self) -> SystemState:
        """确定恢复到的状态"""
        # 根据暂停前的状态决定恢复到哪个状态
        # 处理SystemState枚举或字符串格式
        paused_state_value = None
        if hasattr(self.paused_from_state, 'value'):
            paused_state_value = self.paused_from_state.value
        elif isinstance(self.paused_from_state, str):
            paused_state_value = self.paused_from_state
        elif self.paused_from_state is not None:
            paused_state_value = str(self.paused_from_state)

        self.logger.debug(f"暂停前状态值: {paused_state_value}")

        if paused_state_value == "introduction" or self.paused_from_state == SystemState.INTRODUCTION:
            return SystemState.INTRODUCTION
        elif paused_state_value == "preparation" or self.paused_from_state == SystemState.PREPARATION:
            return SystemState.PREPARATION
        elif paused_state_value == "training" or self.paused_from_state == SystemState.TRAINING:
            return SystemState.TRAINING
        else:
            # 默认恢复到等待状态
            self.logger.warning(f"未知的暂停前状态: {self.paused_from_state} (值: {paused_state_value})，恢复到等待状态")
            return SystemState.WAITING
    
    
    
    def get_pause_info(self) -> Dict[str, Any]:
        """获取暂停信息"""
        pause_duration = time.time() - self.pause_start_time if self.pause_start_time else 0
        
        return {
            "pause_duration": pause_duration,
            "pause_reason": self.pause_reason,
            "paused_from_state": self.paused_from_state,
            "expected_user_id": self.expected_user_id,
            "pause_start_time": self.pause_start_time
        }
    
    def force_resume(self, target_state: SystemState, context: Dict[str, Any]) -> Dict[str, Any]:
        """强制恢复到指定状态（用于异常情况）"""
        pause_duration = time.time() - self.pause_start_time if self.pause_start_time else 0
        
        self.logger.warning(f"强制恢复到 {target_state} 状态，暂停时长: {pause_duration:.1f}秒")
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=target_state,
            message="系统已强制恢复",
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.USER_BACK,
            "next_state": target_state,
            "websocket_message": MessageType.USER_BACK,
            "state_data": state_data,
            "pause_duration": pause_duration,
            "force_resumed": True
        }
    
    def exit_state(self, context: Dict[str, Any]):
        """退出PAUSE状态"""
        pause_duration = time.time() - self.pause_start_time if self.pause_start_time else 0
        
        self.logger.info(f"系统退出暂停状态，暂停时长: {pause_duration:.1f}秒")
        
        # 清理暂停相关信息
        self.pause_start_time = None
        self.paused_from_state = None
        self.pause_reason = None
        self.expected_user_id = None
        
        # 从上下文中清除暂停信息
        context.pop("pause_start_time", None)
        context.pop("paused_from_state", None)
        context.pop("pause_reason", None)
