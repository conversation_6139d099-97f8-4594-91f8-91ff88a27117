
import time
from typing import Dict, Any

from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import ZMQDetectData, SystemStateData, CurrentAction
from ....algorithms.evaluators_factory import evaluator_factory
from . import BaseStateHandler
from ..user_detection_service import user_detection_service
from ..task.task_loader import task_loader

class TrainingHandler(BaseStateHandler):
    """TRAINING状态处理器 - 使用评估器工厂模式"""
    def __init__(self):
        super().__init__(SystemState.TRAINING)
        self.score_threshold_for_completion = 60
        self.completion_duration_target = 2.0
        self.current_evaluator = None
    def enter_state(self, context: Dict[str, Any]):
        self.logger.info("系统进入动作训练状态")
        current_action = context.get("current_action")
        if current_action:
            # 1. 创建当前动作的评估器
            self.current_evaluator = evaluator_factory.create_evaluator(current_action)
            # 2. 初始化动作上下文
            current_action.score = 0
            current_action.completion_progress = 0.0
            current_action.last_valid_time = None
            self.logger.info(f"已加载动作评估器: {current_action.action_type}")

    def handle_data(self, data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        user_status = self._check_user_status(data, context)
        if user_status["should_pause"]:
            return self._handle_pause(user_status, context)
        if not self.current_evaluator:
            self.logger.warning("当前没有可用的动作评估器")
        # 核心逻辑：调用评估器处理数据
        completed_flag = self._process_pose_data(data.pose_keypoints, context)
        if completed_flag:
            next_action_info = task_loader.get_next_action()
            if next_action_info:
                return self._handle_action_switch(context, next_action_info)
            else:
                return self._handle_all_actions_completed(context)
        
        return self._send_training_message(context)
    
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        # 首先检查用户是否在画面中
        presence_result = user_detection_service.check_user(pose_data, context)

        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }
        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        pause_reason = user_status["pause_reason"]

        if pause_reason == "user_lost":
            event = StateTransitionEvent.USER_LOST
            message_type = MessageType.USER_LOST
        else:  # user_not_auth
            event = StateTransitionEvent.USER_NOT_AUTH
            message_type = MessageType.USER_NOT_AUTH

        # 创建暂停状态数据
        state_data = SystemStateData(
            current_state=SystemState.PAUSE,
            message=user_status["message"],
        )
        return {
            "success": True,
            "trigger_event": event,
            "next_state": SystemState.PAUSE,
            "websocket_message": message_type,
            "state_data": state_data,
        }
     
    def _handle_action_switch(self, context: Dict[str, Any], next_action_info: CurrentAction) -> Dict[str, Any]:
        """
        """
        self.logger.info(f"切换到下一个动作: {next_action_info.action_type}")
        context["current_action"] = next_action_info
        context.update({"current_action": next_action_info})
        state_data = SystemStateData(
            current_state=SystemState.PREPARATION,
            message=f"动作完成！准备下一个动作: {next_action_info.action_type}",
            current_action=next_action_info,
        )
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_SWITCH,
            "next_state": SystemState.PREPARATION,
            "websocket_message": MessageType.ACTION_SWITCH,
            "state_data": state_data,
        }
    
    
    def _handle_all_actions_completed(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        【签名修正】处理所有动作完成。不再需要 pose_data 和 training_result。
        """
        self.logger.info("所有训练动作均已完成！")
        final_report_data = self._generate_final_report(context)
        state_data = SystemStateData(
            current_state=SystemState.REPORTING,
            message="恭喜！您已完成所有训练任务。",
            report_data=final_report_data,
        )
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_COMPLETED,
            "next_state": SystemState.REPORTING,
            "websocket_message": MessageType.ACTION_COMPLETED,
            "state_data": state_data,
        }
      # --- 以下方法保持不变或无需修正 ---

    def _generate_final_report(self, context: Dict[str, Any]) -> Dict[str, Any]:
        # ... (此方法签名正确，保持不变) ...
        action_list = context.get("action_list", [])
        total_score = sum(action.score for action in action_list if action.score)
        average_score = total_score / len(action_list) if action_list else 0
        
        report = {
            "average_score": round(average_score, 2),
            "total_actions": len(action_list),
            "completion_time": time.time() - context.get("session_start_time", time.time()),
            "action_details": [a.to_dict() for a in action_list]
        }
        return report

    def _send_training_message(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        【签名修正】发送训练消息。不再需要 pose_data 和 training_result。
        """
        state_data = SystemStateData(
            current_state=SystemState.TRAINING,
            current_action=context.get("current_action"),
        )
        return {
            "success": True,
            "websocket_message": MessageType.TRAINING_MESSAGE,
            "state_data": state_data,
        }

    def _process_pose_data(self, keypoints: list, context: Dict[str, Any]) -> bool:
        """
        使用当前评估器处理姿态数据，并更新完成度。
        :return: 动作是否完成
        """
        current_action = context["current_action"]
        
        # 调用评估器获取评估结果
        result = self.current_evaluator.evaluate(keypoints)
        
        current_action.score = result["score"]
        current_action.feedback_messages = result["feedback"]

        # 更新完成度
        if result["recognized"] and current_action.score >= self.score_threshold_for_completion:
            current_time = time.time()
            if current_action.last_valid_time:
                delta = current_time - current_action.last_valid_time
                current_action.completion_progress += delta / self.completion_duration_target
            current_action.last_valid_time = current_time
            current_action.completion_progress = min(1.0, current_action.completion_progress)
        else:
            current_action.last_valid_time = None # 不达标，重置计时

        if current_action.completion_progress >= 1.0:
            self.logger.info(f"动作 {current_action.action_type} 完成!")
            return True
            
        return False
        
