/**
 * 统一的主数据store
 * 直接映射后端SystemStateData结构，实现基于WebSocket的数据驱动架构
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 移除ElMessage导入，改为响应式消息显示

export const useMainStore = defineStore('main', () => {

  // 直接映射后端SystemStateData的响应式数据
  const currentState = ref('waiting')  // 默认为waiting状态
  const userInfo = ref(null)
  const currentAction = ref(null)
  const actionList = ref([])
  const message = ref('')
  const patientId = ref(null);
  const faceBox = ref(null);

  // 扩展字段（前端需要但后端SystemStateData中没有的）
  const trainingSession = ref(null)
  const poseKeypoints = ref([])
  const reportData = ref(null)  // 训练报告数据

  // 实时数据字段 - 每次接收WebSocket消息都会更新
  const frameData = ref(null)
  const frameCount = ref(0)

  // WebSocket连接状态
  const isConnected = ref(false)
  const connectionError = ref(null)

  // 暂停状态相关
  const isPaused = ref(false)
  const pauseReason = ref(null)
  const pausedFromState = ref(null)

  // 响应式消息显示系统
  const currentNotification = ref(null)
  const notificationHistory = ref([])
  const lastMessageContent = ref('')
  // 计算属性
  const isUserLoggedIn = computed(() => {
    return userInfo.value && userInfo.value.patient_id
  })
  const currentActionName = computed(() => {
    return currentAction.value ? currentAction.value.action_type : null
  })
  /**
   * 统一的WebSocket消息处理器
   * 根据message_type分发处理不同类型的消息
   */
  const handleMessage = (message) => {
    try {
      const { message_type, data, session_id } = message
      // 每次接收消息都更新实时数据（frame_data和pose_keypoints）
      if (data) {
        // 更新帧数据
        if (data.frame_data) {
          frameData.value = data.frame_data
          frameCount.value += 1
        }
        // 更新姿态关键点数据
        if (data.pose_keypoints) {
          poseKeypoints.value = data.pose_keypoints
        }
      }
      // 根据消息类型处理数据 - 更新为新的事件类型
      switch (message_type) {
        case 'system_init':
          handleSystemInit(data)
          break
        case 'waiting_message':
          handleWaitingMessage(data)
          break
        case 'login_success':
          handleLoginSuccess(data)
          break
        case 'training_message':
          handleTrainingMessage(data)
          break
        case 'action_switch':
          handleActionSwitch(data)
          break
        case 'action_completed':
          handleActionCompleted(data)
          break
        case 'user_lost':
          handleUserLost(data)
          break
        case 'user_not_auth':
          handleUserNotAuth(data)
          break
        case 'user_back':
          handleUserBack(data)
          break
        case 'clock_reporting_finish':
          handleClockReportingFinish(data)
          break
        default:
          console.warn('未知的消息类型:', message_type, data)
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error, message)
      connectionError.value = `消息处理错误: ${error.message}`
    }
  }

  /**
   * 处理系统初始化消息
   */
  const handleSystemInit = (data) => {
    // console.log('收到系统初始化消息:', data)
    // 更新系统状态为waiting
    if (data.current_state) {
      currentState.value = data.current_state
    }else{
      currentState.value = "waiting"
    }
    // 更新消息
    if (data.message) {
      message.value = data.message
    }
    // 显示系统初始化消息
    showNotification('success', '系统连接成功，等待用户识别...')
    // 确保路由在waiting状态
  }

  /**
   * 处理等待状态消息
   */
  const handleWaitingMessage = (data) => {
    console.log('收到等待状态消息:', data)
    if (data.patientId) {
      patientId.value = data.patient_id;
    }
    if(data.face_box){
      faceBox.value = data.face_box
    }
   
  }

  /**
   * 处理登录成功消息
   */
  const handleLoginSuccess = (data) => {
    console.log('收到登录成功消息:', data)

    // 更新所有相关数据
    if (data.current_state) currentState.value = data.current_state
    if (data.user_info) userInfo.value = data.user_info
    if (data.current_action) currentAction.value = data.current_action
    if (data.action_list) actionList.value = data.action_list
    // 显示欢迎消息，但不立即跳转
    // 跳转将由LoginView的欢迎动画在3秒后处理
    if (data.user_info && data.user_info.name) {
      showNotification('success', `欢迎 ${data.user_info.name}！正在为您介绍今日训练任务...`)
    }
  }

  /**
   * 处理介绍时钟结束消息
   */
  const handleClockIntroductionFinish = (data) => {
    console.log('收到介绍时钟结束消息:', data)

    // 更新系统状态
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
    if (data.current_action) currentAction.value = data.current_action

    // 跳转到训练页面并开始播放引导视频
   
    showNotification('info', '任务介绍完成，开始播放动作引导视频...')
  }

  /**
   * 处理训练消息
   */
  const handleTrainingMessage = (data) => {
    // console.log('收到训练消息:', data)
    // 更新训练进度
    if (data.message) message.value = data.message
    // 实时更新训练反馈（得分、反馈信息等）
    // 这里不需要显示消息，因为是实时数据
  }

  /**
   * 处理动作切换消息
   */
  const handleActionSwitch = (data) => {
    console.log('收到动作切换消息:', data)
    if (data.current_action) currentAction.value = data.current_action
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message

    // 显示动作切换消息
    if (data.current_action && data.current_action.action_type) {
      showNotification('info', `切换到新动作: ${data.current_action.action_type}`)
        currentState.value = "preparation"
      }
    }
  /**
   * 处理动作完成消息
   */
  const handleActionCompleted = (data) => {
    console.log('收到动作完成消息:', data)

    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
    if (data.progress_info) reportData.value = data.progress_info

    // 显示完成消息并跳转到报告页面
    showNotification('success', '所有动作完成！正在生成训练报告...')
    setTimeout(() => {
       currentState.value = "reporting";
    }, 1000)
  }

  /**
   * 处理用户丢失消息
   */
  const handleUserLost = (data) => {
    console.log('收到用户丢失消息:', data)

    // 更新暂停状态
    isPaused.value = true
    pauseReason.value = 'user_lost'
    pausedFromState.value = currentState.value
    currentState.value = 'pause'

    if (data.message) message.value = data.message

    // 显示暂停提示
    showNotification('warning', '检测不到用户，训练已暂停。请返回摄像头前继续训练。')
  }

  /**
   * 处理用户身份验证失败消息
   */
  const handleUserNotAuth = (data) => {
    console.log('收到用户身份验证失败消息:', data)

    // 更新暂停状态
    isPaused.value = true
    pauseReason.value = 'user_not_auth'
    pausedFromState.value = currentState.value
    currentState.value = 'pause'

    if (data.message) message.value = data.message

    // 显示暂停提示
    showNotification('warning', '检测到非授权用户，训练已暂停。请授权用户返回继续训练。')
  }

  /**
   * 处理用户返回消息
   */
  const handleUserBack = (data) => {
    console.log('收到用户返回消息:', data)

    // 恢复暂停前的状态
    if (data.current_state) {
      currentState.value = data.current_state
      isPaused.value = false
      pauseReason.value = null
      pausedFromState.value = null
    }

    if (data.message) message.value = data.message
    if (data.current_action) currentAction.value = data.current_action
    // 显示恢复消息
    showNotification('success', '欢迎回来！继续您的训练...')
  }

  /**
   * 处理报告时钟结束消息
   */
  const handleClockReportingFinish = (data) => {
    console.log('收到报告时钟结束消息:', data)

    // 清除用户数据，返回等待状态
    currentState.value = 'waiting'
    userInfo.value = null
    currentAction.value = null
    actionList.value = []
    trainingSession.value = null
    reportData.value = null

    if (data.message) message.value = data.message
    // 显示结束消息并跳转到登录页面
    showNotification('info', '训练会话结束，等待下一位用户...')
  }

  /**
   * 响应式消息显示函数
   */
  const showNotification = (type, content, duration = 5000) => {
    // 只有当消息内容真正变化时才更新
    if (content !== lastMessageContent.value) {
      lastMessageContent.value = content

      const notification = {
        id: Date.now(),
        type, // 'success', 'info', 'warning', 'error'
        content,
        timestamp: new Date(),
        duration
      }

      currentNotification.value = notification
      notificationHistory.value.unshift(notification)

      // 限制历史记录数量
      if (notificationHistory.value.length > 10) {
        notificationHistory.value = notificationHistory.value.slice(0, 10)
      }

      // 自动清除通知
      if (duration > 0) {
        setTimeout(() => {
          if (currentNotification.value && currentNotification.value.id === notification.id) {
            currentNotification.value = null
          }
        }, duration)
      }
    }
  }

  /**
   * 重置所有数据
   */
  const resetData = () => {
    currentState.value = 'waiting'
    userInfo.value = null
    currentAction.value = null
    actionList.value = []
    message.value = ''
    trainingSession.value = null
    poseKeypoints.value = []
    reportData.value = null
    frameData.value = null
    frameCount.value = 0
    connectionError.value = null
    isPaused.value = false
    pauseReason.value = null
    pausedFromState.value = null
    currentNotification.value = null
    notificationHistory.value = []
    lastMessageContent.value = ''
  }

  /**
   * 发送preparing_finish事件给后端
   */
  const sendPreparingFinish = () => {
    console.log('发送preparing_finish事件')

    // 通过websocket服务发送事件
    import('@/services/websocket').then(({ websocketService }) => {
      websocketService.send('preparing_finish', {
        timestamp: Date.now(),
        message: '前端引导视频播放完成'
      })
    }).catch(error => {
      console.error('发送preparing_finish事件失败:', error)
    })
     currentState.value = "training";
  }
  const sendIntroductionFinish = () => {
     console.log("发送IntroductionFinish事件");

     // 通过websocket服务发送事件
     import("@/services/websocket")
       .then(({ websocketService }) => {
         websocketService.send("clock_introduction_finish", {
           timestamp: Date.now(),
           message: "任务介绍倒计时完成",
         });
       })
       .catch((error) => {
         console.error("发送preparing_finish事件失败:", error);
       });
       currentState.value = 'preparation';
  }
  /**
   * 设置连接状态
   */
  const setConnectionStatus = (connected, error = null) => {
    isConnected.value = connected
    connectionError.value = error
  }

  return {
    // 响应式数据
    currentState,
    userInfo,
    currentAction,
    actionList,
    message,
    patientId,
    faceBox,
    trainingSession,
    poseKeypoints,
    reportData,
    frameData,
    frameCount,
    isConnected,
    connectionError,
    isPaused,
    pauseReason,
    pausedFromState,

    // 响应式消息系统
    currentNotification,
    notificationHistory,

    // 计算属性
    isUserLoggedIn,
    currentActionName,

    // 方法
    handleMessage,
    resetData,
    setConnectionStatus,
    sendPreparingFinish,
    showNotification,
    sendIntroductionFinish,
    sendPreparingFinish,
  };
})
