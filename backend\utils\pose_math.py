# src/utils/pose_math.py
import numpy as np
from typing import List

class PoseMath:
    @staticmethod
    def calculate_angle(p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> float:
        if p1 is None or p2 is None or p3 is None: return 0.0
        v1 = p1[:2] - p2[:2]
        v2 = p3[:2] - p2[:2]
        dot_product = np.dot(v1, v2)
        norm_v1 = np.linalg.norm(v1)
        norm_v2 = np.linalg.norm(v2)
        if norm_v1 == 0 or norm_v2 == 0: return 0.0
        cosine_angle = dot_product / (norm_v1 * norm_v2)
        angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
        return np.degrees(angle)

    @staticmethod
    def calculate_distance(p1: np.ndarray, p2: np.ndarray) -> float:
        if p1 is None or p2 is None: return float('inf')
        return float(np.linalg.norm(p1[:2] - p2[:2]))

    @staticmethod
    def get_keypoints(keypoints: List[List[float]], indices: List[int], confidence_threshold: float = 0.3) -> List[np.ndarray]:
        if keypoints is None or len(keypoints) == 0:
            return [np.array([0.0, 0.0, 0.0]) for _ in range(len(indices))]
        results = []
        for index in indices:
            if 0 <= index < len(keypoints) and keypoints[index][2] > confidence_threshold:
                results.append(np.array(keypoints[index]))
            else:
                results.append(None)
        return results

pose_math = PoseMath()